{"version": "0.2", "language": "en", "words": ["<PERSON><PERSON>", "VARCHAR", "setex", "<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "numpy", "pydantic", "tensorflow", "pytorch", "sklearn", "opencv", "pillow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "pandas", "scipy", "celery", "redis", "postgresql", "mongodb", "elasticsearch", "kibana", "grafana", "prometheus", "kubernetes", "docker", "nginx", "gunicorn", "wsgi", "asgi", "cors", "csrf", "jwt", "o<PERSON>h", "bcrypt", "argon", "scrypt", "pbkdf", "hmac", "sha", "md5", "ssl", "tls", "https", "http", "tcp", "EDITMSG", "sslmode", "unmatch", "Bitwarden", "<PERSON><PERSON><PERSON><PERSON>", "finalizers", "finalizer", "applicationsets", "kustomization", "Kustomization", "configmap", "THREADPOOL", "xlarge", "CUDA", "tolerations", "letsencrypt", "dashboardproviders", "gnet", "smarthost", "alertname", "xpack", "truststore", "unobserve", "Qube", "qualitygate", "ran<PERSON>t", "dtype", "psutil", "Autobuild", "autobuild", "Semgrep", "returntocorp", "semgrep", "initdb", "tmpfs", "testuser", "testpassword", "sonarcloud", "unittests", "apiservers", "serviceaccount", "numbackends", "mountpoint", "replicasets", "rolebinding", "qrcode", "TOTP", "o<PERSON><PERSON><PERSON>", "totp", "anonymization", "Anonymization", "ANONYMIZATION", "nosniff", "tlds", "alphanum", "originalname", "sonarqube", "sqale", "bitnami", "clickhouse", "runbook", "rubydebug", "zipkin", "daemonset", "psql", "gantt", "cicd", "servicemonitor", "nindent", "lpush", "ltrim", "hsts", "frameguard", "DPIA", "HACCP", "ccps", "monocytogenes", "Exfiltration", "specversion", "datacontenttype", "libc", "appuser", "automount", "aquasecurity", "sarif", "udp", "websocket", "grpc", "restful", "graphql", "json", "yaml", "toml", "xml", "csv", "tsv", "parquet", "avro", "protobuf", "msgpack", "bson", "uuid", "timestamp", "datetime", "timezone", "utc", "iso", "rfc", "mime", "base64", "utf8", "ascii", "unicode", "regex", "xpath", "css", "html", "dom", "svg", "png", "jpg", "jpeg", "gif", "webp", "pdf", "zip", "gzip", "bzip", "tar", "rar", "lz4", "zstd", "snappy", "lzo", "deflate", "inflate", "minify", "uglify", "transpile", "webpack", "rollup", "vite", "babel", "eslint", "prettier", "jest", "mocha", "chai", "sinon", "cypress", "selenium", "puppeteer", "playwright", "storybook", "chromatic", "figma", "sketch", "z<PERSON>lin", "invision", "miro", "lucidchart", "drawio", "plantuml", "mermaid", "graphviz", "d3js", "chartjs", "echarts", "highcharts", "plotly", "bokeh", "altair", "vega", "observablehq", "jup<PERSON><PERSON>", "colab", "kaggle", "huggingface", "wandb", "mlflow", "tensorboard", "visdom", "neptune", "comet", "clearml", "dvc", "git", "github", "gitlab", "bitbucket", "svn", "mercurial", "perforce", "bazaar", "cvs", "rcs", "sccs", "ci", "cd", "devops", "gitops", "terraform", "ansible", "puppet", "chef", "saltstack", "vagrant", "packer", "consul", "vault", "nomad", "etcd", "zookeeper", "kafka", "rabbitmq", "activemq", "zeromq", "nats", "pulsar", "kinesis", "pubsub", "eventbridge", "sns", "sqs", "lambda", "cloudformation", "cloudwatch", "cloudtrail", "iam", "vpc", "ec2", "ecs", "eks", "fargate", "rds", "dynamodb", "s3", "cloudfront", "route53", "elb", "alb", "nlb", "api", "gateway", "cognito", "amplify", "appsync", "stepfunctions", "eventbridge", "xray", "codebuild", "codedeploy", "codepipeline", "codecommit", "codestar", "cloud9", "workspaces", "appstream", "workdocs", "workmail", "chime", "connect", "pinpoint", "ses", "workspaces", "fsx", "efs", "ebs", "glacier", "snowball", "snowmobile", "datasync", "storagegateway", "backup", "disaster", "recovery", "availability", "durability", "consistency", "eventual", "strong", "weak", "causal", "monotonic", "linearizable", "serializable", "snapshot", "isolation", "atomicity", "durability", "acid", "base", "cap", "theorem", "partition", "tolerance", "sharding", "replication", "failover", "loadbalancing", "autoscaling", "horizontal", "vertical", "scaling", "microservices", "monolith", "serverless", "containerization", "orchestration", "service", "mesh", "istio", "linkerd", "envoy", "traefik", "haproxy", "nginx", "apache", "tomcat", "jetty", "undertow", "netty", "vert", "akka", "spring", "boot", "framework", "hibernate", "mybatis", "jpa", "jdbc", "odbc", "oledb", "ado", "entity", "orm", "odm", "dao", "dto", "pojo", "bean", "dependency", "injection", "inversion", "control", "aspect", "oriented", "programming", "aop", "solid", "principles", "dry", "kiss", "yagni", "mvc", "mvp", "mvvm", "flux", "redux", "mobx", "vuex", "ngrx", "rxjs", "observable", "promise", "async", "await", "callback", "closure", "hoisting", "prototype", "inheritance", "polymorphism", "encapsulation", "abstraction", "interface", "abstract", "class", "method", "function", "variable", "constant", "immutable", "mutable", "pure", "impure", "side", "effect", "functional", "imperative", "declarative", "procedural", "object", "oriented", "paradigm", "algorithm", "data", "structure", "array", "list", "stack", "queue", "tree", "graph", "hash", "table", "map", "set", "heap", "priority", "binary", "search", "sort", "merge", "quick", "bubble", "insertion", "selection", "radix", "counting", "bucket", "heap", "shell", "cocktail", "gnome", "comb", "pigeonhole", "bogo", "stooge", "pancake", "spaghetti", "sleep", "stupid", "miracle", "quantum", "bogobogo", "slowsort", "permutation", "lexicographic", "topological", "breadth", "first", "depth", "<PERSON><PERSON><PERSON>", "bellman", "ford", "floyd", "warshall", "kruskal", "prim", "bor<PERSON><PERSON>ka", "tarjan", "kosa<PERSON><PERSON>", "strongly", "connected", "components", "articulation", "points", "bridges", "biconnected", "eulerian", "hamiltonian", "path", "cycle", "spanning", "minimum", "maximum", "flow", "network", "bipartite", "matching", "hungarian", "kuhn", "munkres", "<PERSON><PERSON>", "karp", "edmonds", "blossom", "ford", "<PERSON><PERSON><PERSON><PERSON>", "dinic", "push", "relabel", "preflow", "goldberg", "tarjan", "dynamic", "programming", "memoization", "tabulation", "greedy", "divide", "conquer", "backtracking", "branch", "bound", "brute", "force", "exhaustive", "heuristic", "metaheuristic", "genetic", "simulated", "annealing", "tabu", "particle", "swarm", "optimization", "ant", "colony", "artificial", "intelligence", "machine", "learning", "deep", "neural", "network", "perceptron", "multilayer", "backpropagation", "gradient", "descent", "stochastic", "adam", "rmsprop", "adagrad", "adadelta", "momentum", "<PERSON><PERSON><PERSON>", "learning", "rate", "decay", "dropout", "batch", "normalization", "layer", "instance", "group", "weight", "standardization", "activation", "function", "sigmoid", "tanh", "relu", "leaky", "elu", "selu", "gelu", "swish", "mish", "softmax", "softplus", "softsign", "hardtanh", "hardsigmoid", "hardswish", "prelu", "rrelu", "celu", "glu", "convolution", "pooling", "max", "average", "global", "adaptive", "fractional", "dilated", "atrous", "depthwise", "separable", "pointwise", "grouped", "transposed", "deconvolution", "upsampling", "interpolation", "nearest", "bilinear", "bicubic", "trilinear", "area", "lanc<PERSON>s", "hamming", "hanning", "blackman", "kaiser", "gaussian", "laplacian", "sobel", "<PERSON><PERSON><PERSON>", "roberts", "canny", "harris", "corner", "detector", "sift", "surf", "orb", "brief", "brisk", "freak", "akaze", "kaze", "fast", "good", "features", "track", "lucas", "kanade", "optical", "flow", "horn", "schu<PERSON><PERSON>", "farneback", "dense", "sparse", "pyramid", "iterative", "refinement", "template", "matching", "normalized", "cross", "correlation", "squared", "difference", "coefficient", "ccorr", "sqdiff", "<PERSON><PERSON><PERSON>", "histogram", "equalization", "clahe", "contrast", "limited", "adaptive", "thresholding", "otsu", "triangle", "entropy", "minimum", "moments", "yen", "isodata", "li", "huang", "renyi", "<PERSON><PERSON><PERSON><PERSON>", "intermodes", "maximum", "entropy", "kapur", "sahoo", "wong", "rosin", "morphological", "operations", "erosion", "dilation", "opening", "closing", "gradient", "tophat", "blackhat", "reconstruction", "watershed", "distance", "transform", "euclidean", "manhattan", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minkowski", "hamming", "j<PERSON><PERSON>", "dice", "cosine", "similarity", "pearson", "spearman", "kendall", "mutual", "information", "kullback", "leibler", "divergence", "<PERSON><PERSON><PERSON>", "shannon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellinger", "<PERSON><PERSON><PERSON>", "earth", "mover", "optimal", "transport", "sinkhorn", "<PERSON><PERSON><PERSON><PERSON>", "monge", "<PERSON><PERSON><PERSON>", "hausdorff", "frechet", "procrustes", "<PERSON><PERSON>ch", "<PERSON>ey<PERSON>", "icp", "iterative", "closest", "point", "ransac", "random", "sample", "consensus", "msac", "mlesac", "lmeds", "least", "median", "squares", "prosac", "progressive", "sampling", "lo", "locally", "optimized", "usac", "universal", "robust", "estimation", "magsac", "marginalizing", "sample", "<PERSON><PERSON><PERSON>", "degeneracy", "aware", "gc", "graph", "cut", "alpha", "expansion", "beta", "swap", "belief", "propagation", "loopy", "tree", "reweighted", "message", "passing", "junction", "elimination", "variable", "factor", "markov", "random", "field", "conditional", "gibbs", "<PERSON><PERSON><PERSON>", "machine", "restricted", "deep", "belief", "network", "autoencoder", "variational", "generative", "adversarial", "discriminator", "generator", "<PERSON><PERSON><PERSON>", "least", "squares", "progressive", "growing", "stylegan", "biggan", "<PERSON><PERSON>", "pix2pix", "image", "translation", "super", "resolution", "srcnn", "vdsr", "drcn", "red", "memnet", "srgan", "esrgan", "<PERSON>r", "rcan", "san", "han", "rnan", "nlrn", "dbpn", "zssr", "meta", "sisr", "liif", "arbitrary", "scale", "denoising", "dncnn", "ffdnet", "ridnet", "rdn", "nlrn", "cbdnet", "vdnet", "dncnn", "ircnn", "drunet", "restormer", "nafnet", "maxim", "uformer", "swin<PERSON>", "hat", "dat", "elan", "rlfn", "plksr", "span", "compact", "efficient", "lightweight", "mobile", "quantization", "pruning", "distillation", "compression", "acceleration", "optimization", "tensorrt", "<PERSON>vin<PERSON>", "onnx", "tflite", "coreml", "ncnn", "mnn", "paddle", "lite", "mindspore", "oneflow", "megengine", "jittor", "parrots", "detectron", "mmdetection", "yolo", "rcnn", "fast", "faster", "mask", "cascade", "fcos", "centernet", "cornernet", "extremenet", "foveabox", "fsaf", "guided", "anchoring", "reppoints", "freeanchor", "autoassign", "ota", "simota", "tood", "gfl", "ld", "atss", "paa", "gfocal", "vfnet", "borderdet", "sparse", "rcnn", "querydet", "adamixer", "conditional", "detr", "deformable", "efficient", "anchor", "dn", "dab", "dino", "group", "rtdetr", "co", "dino", "grounding", "glip", "mdetr", "owl", "vit", "clip", "align", "blip", "flamingo", "kosmos", "gpt4v", "llava", "minigpt", "instructblip", "qwen", "internlm", "xcomposer", "cogvlm", "sharegpt4v", "llavanext", "cogagent", "internvl", "minicpm", "deepseek", "vl", "phi", "vision", "pix<PERSON>", "molmo", "aria", "nvlm", "llama", "vision", "qwen2", "vl", "internvl2", "mllama", "cambrian", "eagle", "mantis", "vila", "videollama", "videochat", "valley", "macaw", "lw", "pllava", "moviechat", "videochatgpt", "llama<PERSON>", "chat", "univi", "onellm", "languagebind", "imagebind", "anymal", "pandagpt", "lynx", "nextgpt", "macaw", "lw", "speechgpt", "viola", "pengi", "x", "llm", "gpt", "bert", "roberta", "electra", "deberta", "<PERSON>bert", "<PERSON><PERSON><PERSON><PERSON>", "mobilebert", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "well<PERSON><PERSON>", "s<PERSON><PERSON>", "<PERSON><PERSON>", "longformer", "bigbird", "reformer", "linformer", "performer", "synthesizer", "fnet", "afno", "gmlp", "mlpmixer", "resmlp", "gfnet", "convmixer", "poolformer", "metaformer", "caformer", "convnext", "efficientnet", "regnet", "resnext", "resnet", "densenet", "mobilenet", "shufflenet", "squeezenet", "ghostnet", "mnasnet", "fbnet", "proxylessnas", "mixnet", "efficientnetv2", "nfnet", "regnetx", "regnety", "<PERSON><PERSON><PERSON>", "resnetd", "resnest", "rexnet", "tresnet", "h<PERSON>t", "hardnet", "darknet", "cspdarknet", "c<PERSON><PERSON><PERSON>", "cspresnext", "cspdensenet", "yolov3", "yolov4", "yolov5", "yolov6", "yolov7", "yolov8", "yolov9", "yolov10", "yolox", "yolor", "yolof", "ppyolo", "ppyoloe", "rtmdet", "nanodet", "picodet", "centernet", "cornernet", "extremenet", "bottomup", "topdown", "hrpose", "simplebaseline", "hourglass", "cpn", "cpm", "openpose", "alphapose", "poseflow", "lighttrack", "pifpaf", "directpose", "dekr", "higherhrnet", "associative", "embedding", "personlab", "posenet", "denspose", "roi", "heads", "parsing", "human", "ce2p", "schp", "cdgnet", "bgnet", "aisegmentcom", "segment", "anything", "sam", "fastsam", "mobilesam", "efficientSAM", "slimSAM", "edgeSAM", "repvit", "sam", "sam2", "cutie", "xmem", "stm", "aot", "<PERSON><PERSON><PERSON>", "swem", "rde", "vos", "siammask", "d3s", "dimp", "prdimp", "tomp", "keep", "track", "mixformer", "ostrack", "seqtrack", "simtrack", "unicorn", "artrack", "vittrack", "stark", "transt", "trtr", "swintrack", "cs<PERSON><PERSON>", "autoregressive", "transformer", "encoder", "decoder", "attention", "self", "cross", "multi", "head", "scaled", "dot", "product", "additive", "multiplicative", "location", "based", "content", "b<PERSON><PERSON><PERSON>", "luong", "<PERSON><PERSON><PERSON>", "monotonic", "stepwise", "local", "global", "sparse", "dense", "linear", "logarithmic", "constant", "quadratic", "exponential", "polynomial", "factorial", "combinatorial", "permutation", "combination", "binomial", "multinomial", "hypergeometric", "negative", "poisson", "exponential", "gamma", "beta", "<PERSON><PERSON><PERSON>", "<PERSON>art", "inverse", "chi", "squared", "student", "fisher", "snedecor", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "anderson", "darling", "s<PERSON><PERSON><PERSON>", "wilk", "<PERSON><PERSON>", "bera", "lilliefors", "cramer", "von", "mises", "kuiper", "watson", "mann", "whitney", "wil<PERSON><PERSON>", "kruskal", "wallis", "friedman", "cochran", "mcnemar", "fisher", "exact", "barnard", "boschloo", "z", "test", "t", "test", "f", "test", "anova", "<PERSON><PERSON><PERSON>", "<PERSON>ova", "manco<PERSON>", "regression", "linear", "logistic", "polynomial", "ridge", "lasso", "elastic", "net", "lars", "lasso", "lars", "omp", "orthogonal", "matching", "pursuit", "bayesian", "ridge", "ard", "automatic", "relevance", "determination", "gaussian", "process", "kernel", "ridge", "svr", "support", "vector", "regression", "nu", "svr", "linear", "svr", "svm", "classification", "nu", "svc", "linear", "svc", "sgd", "classifier", "regressor", "perceptron", "passive", "aggressive", "nearest", "neighbors", "knn", "radius", "neighbors", "kd", "tree", "ball", "tree", "lsh", "locality", "sensitive", "hashing", "random", "projection", "johnson", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sparse", "random", "projection", "gaussian", "random", "projection", "pca", "principal", "component", "analysis", "kernel", "pca", "sparse", "pca", "incremental", "pca", "mini", "batch", "sparse", "pca", "truncated", "svd", "singular", "value", "decomposition", "randomized", "svd", "dictionary", "learning", "sparse", "coding", "mini", "batch", "dictionary", "learning", "factor", "analysis", "fast", "ica", "independent", "component", "analysis", "non", "negative", "matrix", "factorization", "nmf", "COMPFIX", "direnv", "DIRENV", "acasis", "fpath", "codeium", "lmstudio", "cmdline", "INSIGHTFACE", "insightface", "ctype", "healthcheck", "LIMS", "cjrp", "pids", "wscript", "eslintcache", "<PERSON><PERSON><PERSON>", "nuxt", "pycache", "sdist", "htmlcov", "nosetests", "webassets", "Scrapy", "scrapy", "ipynb", "ipython", "celerybeat", "venv", "<PERSON>der", "spyproject", "ropeproject", "mkdocs", "mypy", "dmypy", "joblib", "ckpt", "fseventsd", "icns", "timemachine", "donotpresent", "apdisk", "ehthumbs", "tfstate", "kubeconfig", "spyderproject", "latent", "<PERSON><PERSON><PERSON>", "allocation", "lda", "quadratic", "discriminant", "analysis", "qda", "linear", "discriminant", "analysis", "lda", "naive", "bayes", "gaussian", "nb", "multinomial", "nb", "complement", "nb", "<PERSON><PERSON><PERSON><PERSON>", "nb", "categorical", "nb", "decision", "tree", "classifier", "regressor", "extra", "tree", "classifier", "regressor", "random", "forest", "classifier", "regressor", "extra", "trees", "classifier", "regressor", "gradient", "boosting", "classifier", "regressor", "hist", "gradient", "boosting", "classifier", "regressor", "ada", "boost", "classifier", "regressor", "bagging", "classifier", "regressor", "voting", "classifier", "regressor", "stacking", "classifier", "regressor", "multi", "output", "classifier", "regressor", "multi", "target", "classifier", "regressor", "classifier", "chain", "regressor", "chain", "one", "vs", "rest", "classifier", "one", "vs", "one", "classifier", "output", "code", "classifier", "multi", "class", "multi", "label", "binary", "relevance", "label", "powerset", "adapted", "algorithm", "ml", "knn", "br", "knn", "cc", "knn", "mlaram", "meka", "mulan", "scikit", "multilearn", "clustering", "kmeans", "mini", "batch", "kmeans", "affinity", "propagation", "mean", "shift", "spectral", "clustering", "agglomerative", "clustering", "dbscan", "optics", "birch", "gaussian", "mixture", "bayesian", "gaussian", "mixture", "anomaly", "detection", "outlier", "detection", "novelty", "detection", "one", "class", "svm", "isolation", "forest", "local", "outlier", "factor", "lof", "elliptic", "envelope", "robust", "covariance", "minimum", "covariance", "determinant", "mcd", "empirical", "covariance", "shrunk", "covariance", "<PERSON><PERSON>", "wolf", "oas", "oracle", "approximating", "shrinkage", "graphical", "lasso", "sparse", "inverse", "covariance", "cross", "validation", "kfold", "stratified", "kfold", "repeated", "kfold", "repeated", "stratified", "kfold", "leave", "one", "out", "loo", "leave", "p", "out", "lpo", "shuffle", "split", "stratified", "shuffle", "split", "group", "kfold", "stratified", "group", "kfold", "leave", "one", "group", "out", "logo", "leave", "p", "groups", "out", "lpgo", "group", "shuffle", "split", "time", "series", "split", "predefined", "split", "train", "test", "split", "validation", "curve", "learning", "curve", "permutation", "test", "score", "grid", "search", "cv", "randomized", "search", "cv", "halving", "grid", "search", "cv", "halving", "random", "search", "cv", "bayesian", "optimization", "hyperopt", "optuna", "skopt", "scikit", "optimize", "hyperband", "bohb", "population", "based", "training", "pbt", "asha", "asynchronous", "successive", "halving", "algorithm", "median", "stopping", "rule", "early", "stopping", "patience", "min", "delta", "restore", "best", "weights", "verbose", "mode", "baseline", "monitor", "cooldown", "min", "lr", "epsilon", "threshold", "threshold", "mode", "reduce", "lr", "on", "plateau", "factor", "patience", "verbose", "mode", "min", "delta", "cooldown", "min", "lr", "eps", "cosine", "annealing", "warm", "restarts", "t", "max", "eta", "min", "t", "mult", "last", "epoch", "verbose", "cyclic", "lr", "base", "lr", "max", "lr", "step", "size", "up", "step", "size", "down", "mode", "gamma", "scale", "fn", "scale", "mode", "cycle", "momentum", "base", "momentum", "max", "momentum", "last", "epoch", "verbose", "one", "cycle", "lr", "max", "lr", "total", "steps", "epochs", "steps", "per", "epoch", "pct", "start", "anneal", "strategy", "cycle", "momentum", "base", "momentum", "max", "momentum", "div", "factor", "final", "div", "factor", "three", "phase", "last", "epoch", "verbose", "lambda", "lr", "lr", "lambda", "last", "epoch", "verbose", "multiplicative", "lr", "lr", "lambda", "last", "epoch", "verbose", "step", "lr", "step", "size", "gamma", "last", "epoch", "verbose", "multi", "step", "lr", "milestones", "gamma", "last", "epoch", "verbose", "exponential", "lr", "gamma", "last", "epoch", "verbose", "cosine", "annealing", "lr", "t", "max", "eta", "min", "last", "epoch", "verbose", "polynomial", "lr", "total", "iters", "power", "last", "epoch", "verbose", "sequential", "lr", "schedulers", "milestones", "gamma", "last", "epoch", "verbose", "chained", "scheduler", "schedulers", "constant", "lr", "factor", "total", "iters", "last", "epoch", "verbose", "linear", "lr", "start", "factor", "end", "factor", "total", "iters", "last", "epoch", "verbose", "inverse", "sqrt", "lr", "warmup", "init", "lr", "warmup", "steps", "last", "epoch", "verbose", "noam", "lr", "model", "size", "warmup", "steps", "last", "epoch", "verbose", "rsqrt", "lr", "warmup", "init", "lr", "warmup", "steps", "last", "epoch", "verbose", "asyncio", "keras", "n<PERSON><PERSON>", "isoformat", "getenv", "hostnames", "addgroup", "adduser", "libgl", "libglib", "libgtk", "libqt", "libx11", "libxcb", "libxext", "libxrender", "libxrandr", "libxinerama", "libxss", "libxcomposite", "libxdamage", "libxfixes", "libxcursor", "libxi", "libxtst", "libasound", "libpulse", "libjack", "libavcodec", "libavformat", "liba<PERSON><PERSON>", "libswscale", "libswresample", "ffmpeg", "opencv", "gstreamer", "v4l2", "alsa", "pulseaudio", "jackd", "pipewire", "wayland", "xorg", "systemd", "dbus", "udev", "polkit", "networkmanager", "bluez", "avahi", "cups", "samba", "nfs", "cifs", "fuse", "ntfs", "exfat", "btrfs", "zfs", "lvm", "mdadm", "cryptsetup", "luks", "dmcrypt", "iptables", "netfilter", "selinux", "apparmor", "grsecurity", "pax", "hardening", "seccomp", "namespaces", "cgroups", "chroot", "jail", "sandbox", "container", "virtualization", "hypervisor", "kvm", "qemu", "xen", "vmware", "virtualbox", "parallels", "hyper", "lxc", "lxd", "runc", "containerd", "cri", "oci", "buildah", "podman", "skopeo", "registry", "harbor", "quay", "gcr", "ecr", "acr", "docker<PERSON>b", "artifactory", "nexus", "sonatype", "jfrog", "helm", "kustomize", "skaffold", "tilt", "devspace", "okteto", "telepresence", "minikube", "kind", "k3s", "k3d", "microk8s", "rancher", "openshift", "eks", "gke", "aks", "doks", "linode", "vultr", "<PERSON><PERSON><PERSON>", "scaleway", "ovh", "digitalocean", "cloudflare", "fastly", "maxcdn", "keycdn", "bunny<PERSON><PERSON>", "js<PERSON><PERSON><PERSON>", "unpkg", "cdnjs", "bootcdn", "staticfile", "<PERSON><PERSON><PERSON><PERSON>", "360cdn", "bytedance", "tencent", "alibaba", "hua<PERSON>", "baidu", "qiniu", "upyun", "netease", "sina", "sohu", "<PERSON><PERSON><PERSON>", "youku", "tudou", "bilibili", "do<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zhihu", "weibo", "wechat", "qq", "<PERSON><PERSON><PERSON>", "feishu", "lark", "slack", "discord", "telegram", "whatsapp", "signal", "matrix", "element", "riot", "mattermost", "rocketchat", "<PERSON><PERSON>", "gitter", "irc", "xmpp", "jabber", "sip", "webrtc", "stun", "turn", "ice", "dtls", "srtp", "rtp", "rtcp", "rtmp", "rtsp", "hls", "dash", "webm", "mp4", "avi", "mkv", "mov", "wmv", "flv", "ogv", "3gp", "m4v", "ts", "m3u8", "mpd", "ism", "isml", "f4v", "f4p", "f4a", "f4b", "libsm", "libgomp", "alertmanager", "alertmanagers", "isready", "pytest", "ossp", "libomp", "libiomp", "mkl", "openblas", "lapack", "blas", "atlas", "eigen", "armadillo", "gsl", "fftw", "hdf5", "netcdf", "protobuf", "flatbuffers", "capnproto", "msgpack", "avro", "thrift", "grpc", "zeromq", "nanomsg", "libzmq", "czmq", "pyzmq", "jzmq", "libnanomsg", "nng", "mangos", "libcurl", "libssl", "openssl", "libressl", "boringssl", "mbedtls", "wolfssl", "gnutls", "nss", "libgcrypt", "libgpg", "gpgme", "libsodium", "tweetnacl", "libsecp256k1", "ed25519", "curve25519", "x25519", "chacha20", "poly1305", "salsa20", "xchacha20", "blake2", "blake3", "sha3", "keccak", "whirlpool", "ripemd", "tiger", "skein", "gro<PERSON>l", "jh", "<PERSON><PERSON><PERSON>", "fugue", "hamsi", "luffa", "shabal", "shavite", "simd", "echo", "bmw", "argon2", "scrypt", "pbkdf2", "bcrypt", "yescrypt", "lyra2", "catena", "makwa", "phs", "balloon", "pomelo", "battcrypt", "parallel", "gambit", "tortuga", "antcrypt", "centrifuge", "lanarea", "rig", "schvrch", "yarn", "pnpm", "bun", "deno", "nodejs", "npm", "npx", "nvm", "volta", "fnm", "asdf", "mise", "rtx", "pyenv", "rbenv", "rvm", "gvm", "jenv", "sdkman", "rustup", "cargo", "rustc", "clippy", "rustfmt", "miri", "bindgen", "c<PERSON><PERSON>n", "wasm", "wasmer", "wasmtime", "wasi", "emscripten", "binaryen", "wabt", "wat", "wast", "witx", "wit", "<PERSON><PERSON><PERSON><PERSON>", "cranelift", "lucet", "wascc", "wapc", "assemblyscript", "<PERSON>go", "grain", "moonbit", "zig", "odin", "nim", "crystal", "elixir", "erlang", "gleam", "pony", "red", "rebol", "factor", "forth", "postscript", "tcl", "tk", "expect", "itcl", "incr", "tclx", "blt", "tix", "tkinter", "ttk", "ttkthemes", "customtkinter", "tkdnd", "pmw", "megawidgets", "bwidget", "iwidgets", "tablelist", "treectrl", "tkhtml", "tkimg", "tkpng", "tkjpeg", "tkgif", "tkbmp", "tktiff", "tkxpm", "tkppm", "tkps", "tksvg", "tkpath", "tkzinc", "tktable", "tkcon", "tkdiff", "tkcvs", "tkrev", "tkrat", "tkmail", "tknews", "tkftp", "tkwww", "tkhtml3", "tkdnd2", "tklib", "tcllib", "critcl", "snit", "sto<PERSON>", "xotcl", "nx", "itk", "iwidgets4", "incrwidgets", "Bifidobacterium", "Kombucha", "mindmap", "Mbps", "<PERSON><PERSON>", "R<PERSON>zen"], "flagWords": [], "ignorePaths": ["node_modules/**", ".git/**", "dist/**", "build/**", "*.min.js", "*.min.css"], "ignoreRegExpList": ["/\\b[A-Z]{2,}\\b/g", "/\\b\\d+\\b/g", "/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g", "/https?:\\/\\/[^\\s]+/g"]}
# 🔐 Yoghurt AI QC - 安全配置指南

## 概述

本指南旨在帮助开发者安全地管理 Yoghurt AI QC 项目中的敏感信息，避免将密钥、密码等敏感数据暴露在代码库中。

## 🚨 发现的安全问题

### 1. PostgreSQL 数据库密码泄露
- **位置**: `.env` 文件第 18 行
- **问题**: 数据库密码 `R4t7cjrp` 直接暴露在代码中
- **风险等级**: 高

### 2. Google API 密钥泄露
- **位置**: `.env` 文件第 56 行
- **问题**: Google API 密钥直接暴露在代码中
- **风险等级**: 高

## ✅ 安全解决方案

### 1. 立即行动

#### 步骤 1: 更换暴露的凭据
```bash
# 1. 立即更改数据库密码
# 连接到 PostgreSQL 并执行:
ALTER USER yoghurt_user PASSWORD 'NEW_SECURE_PASSWORD_HERE';

# 2. 撤销并重新生成 Google API 密钥
# 访问 Google Cloud Console:
# https://console.cloud.google.com/apis/credentials
```

#### 步骤 2: 使用安全的环境变量模板
```bash
# 备份当前的 .env 文件
cp .env .env.backup

# 使用安全模板替换当前配置
cp .env.secure .env

# 编辑 .env 文件，填入真实的安全凭据
vim .env
```

### 2. 环境变量安全最佳实践

#### 密码强度要求
- **数据库密码**: 至少 16 字符，包含大小写字母、数字和特殊字符
- **JWT 密钥**: 至少 32 字符的随机字符串
- **加密密钥**: 恰好 32 字符的随机字符串

#### 生成安全密码示例
```bash
# 生成强密码
openssl rand -base64 32

# 生成 32 字符的十六进制密钥
openssl rand -hex 16

# 使用 Node.js 生成随机字符串
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### 3. 版本控制安全

#### 更新 .gitignore
确保以下文件不会被提交到版本控制:
```gitignore
# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 备份文件
.env.backup
*.env.backup

# 密钥文件
*.key
*.pem
*.p12
*.pfx

# 配置文件中的敏感信息
config/secrets.json
config/production.json
```

#### 检查历史提交
```bash
# 检查是否有敏感信息被提交到历史记录
git log --all --full-history -- .env

# 如果发现敏感信息在历史中，需要清理历史记录
# 警告：这会重写 Git 历史，需要谨慎操作
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch .env' \
  --prune-empty --tag-name-filter cat -- --all
```

### 4. 生产环境安全

#### 使用环境变量管理服务
- **AWS**: AWS Secrets Manager, AWS Systems Manager Parameter Store
- **Azure**: Azure Key Vault
- **Google Cloud**: Secret Manager
- **Docker**: Docker Secrets
- **Kubernetes**: Kubernetes Secrets

#### Docker 生产环境配置
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  backend:
    environment:
      - DB_PASSWORD_FILE=/run/secrets/db_password
      - GOOGLE_API_KEY_FILE=/run/secrets/google_api_key
    secrets:
      - db_password
      - google_api_key

secrets:
  db_password:
    external: true
  google_api_key:
    external: true
```

### 5. 开发环境安全

#### 本地开发配置
```bash
# 创建本地环境变量文件
cp .env.example .env.local

# 设置适当的文件权限
chmod 600 .env.local

# 在 shell 配置中设置环境变量
echo 'export YOGHURT_DB_PASSWORD="your-secure-password"' >> ~/.zshrc
source ~/.zshrc
```

#### 团队协作
1. **永远不要**在聊天工具中分享密钥
2. 使用安全的密钥管理工具（如 1Password、Bitwarden）
3. 定期轮换密钥和密码
4. 为每个环境使用不同的凭据

### 6. 监控和审计

#### 设置安全监控
```bash
# 监控敏感文件的变化
git config --global core.hooksPath .githooks

# 创建 pre-commit 钩子检查敏感信息
cat > .githooks/pre-commit << 'EOF'
#!/bin/bash
if git diff --cached --name-only | grep -E "\.(env|key|pem)$"; then
    echo "警告：检测到敏感文件，请确认是否应该提交"
    exit 1
fi
EOF

chmod +x .githooks/pre-commit
```

#### 定期安全审计
- 每月检查和轮换 API 密钥
- 审查访问日志
- 更新依赖包以修复安全漏洞
- 进行渗透测试

## 📋 安全检查清单

- [ ] 已更换所有暴露的密码和 API 密钥
- [ ] 已更新 .gitignore 文件
- [ ] 已检查 Git 历史记录中的敏感信息
- [ ] 已设置适当的文件权限
- [ ] 已配置环境变量管理
- [ ] 已设置安全监控
- [ ] 团队成员已接受安全培训

## 🆘 紧急响应

如果发现密钥泄露：

1. **立即撤销**暴露的密钥
2. **生成新的**密钥和密码
3. **更新所有**使用该密钥的服务
4. **审查日志**查找可疑活动
5. **通知团队**和相关利益相关者
6. **记录事件**并改进安全流程

## 📞 联系信息

如有安全问题或疑虑，请联系：
- 安全团队：<EMAIL>
- 项目负责人：<EMAIL>

---

**记住：安全是每个人的责任！** 🛡️
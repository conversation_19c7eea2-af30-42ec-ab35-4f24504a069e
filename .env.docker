# =============================================================================
# Docker Compose Environment Variables
# =============================================================================
# This file contains environment variables required for Docker Compose
# Copy this file to .env.docker.local and fill in actual values
# DO NOT commit .env.docker.local to version control

# =============================================================================
# Database Configuration
# =============================================================================
# PostgreSQL database password
# Used by both postgres service and backend service
DB_PASSWORD=YOUR_SECURE_DB_PASSWORD_HERE

# =============================================================================
# Authentication Configuration
# =============================================================================
# JWT secret key for token signing
# Must be at least 32 characters long
JWT_SECRET=YOUR_SECURE_JWT_SECRET_KEY_AT_LEAST_32_CHARACTERS_LONG

# =============================================================================
# AI Service Configuration
# =============================================================================
# OpenAI API key for AI analysis features
OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE

# =============================================================================
# Security Notes
# =============================================================================
# 1. Generate strong passwords with at least 12 characters
# 2. Use a cryptographically secure JWT secret
# 3. Keep API keys confidential and rotate them regularly
# 4. Never commit actual secrets to version control
# 5. Use different secrets for different environments

# =============================================================================
# Example Commands to Generate Secure Values
# =============================================================================
# Generate a secure database password:
# openssl rand -base64 32
#
# Generate a secure JWT secret:
# openssl rand -base64 64
#
# Verify your environment variables:
# docker-compose config
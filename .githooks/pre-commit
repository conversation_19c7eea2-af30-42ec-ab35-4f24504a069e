#!/bin/bash

# =============================================================================
# Yoghurt AI QC - Pre-commit 安全检查钩子
# =============================================================================
# 此脚本在每次提交前运行，检查是否包含敏感信息

set -e

# 颜色定义
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔍 运行安全检查...${NC}"

# 检查是否有敏感文件被暂存
echo "检查敏感文件..."
SENSITIVE_FILES=$(git diff --cached --name-only | grep -E "\.(env|key|pem|crt|p12|pfx)$" || true)
if [ ! -z "$SENSITIVE_FILES" ]; then
    echo -e "${RED}❌ 错误：检测到敏感文件被暂存：${NC}"
    echo "$SENSITIVE_FILES"
    echo -e "${YELLOW}请确认这些文件是否应该被提交到版本控制中${NC}"
    exit 1
fi

# 检查环境变量文件内容
echo "检查环境变量文件内容..."
ENV_FILES=$(git diff --cached --name-only | grep -E "\.env" || true)
if [ ! -z "$ENV_FILES" ]; then
    echo -e "${YELLOW}⚠️  警告：检测到 .env 文件变更${NC}"
    echo "请确认没有包含真实的密钥或密码"
fi

# 检查代码中的潜在密钥模式
echo "检查代码中的潜在密钥..."
SUSPICIOUS_PATTERNS=(
    "password\s*=\s*['\"][^'\"]{8,}['\"]"  # 密码模式
    "api[_-]?key\s*=\s*['\"][^'\"]{20,}['\"]"  # API密钥模式
    "secret\s*=\s*['\"][^'\"]{16,}['\"]"  # 密钥模式
    "token\s*=\s*['\"][^'\"]{20,}['\"]"  # 令牌模式
    "sk-[a-zA-Z0-9]{20,}"  # OpenAI API密钥模式
    "AIza[0-9A-Za-z\\-_]{35}"  # Google API密钥模式
    "AKIA[0-9A-Z]{16}"  # AWS访问密钥模式
)

for pattern in "${SUSPICIOUS_PATTERNS[@]}"; do
    MATCHES=$(git diff --cached | grep -iE "$pattern" || true)
    if [ ! -z "$MATCHES" ]; then
        echo -e "${RED}❌ 错误：检测到可疑的密钥模式：${NC}"
        echo "$MATCHES"
        echo -e "${YELLOW}请移除硬编码的密钥并使用环境变量${NC}"
        exit 1
    fi
done

# 检查大文件
echo "检查大文件..."
LARGE_FILES=$(git diff --cached --name-only | xargs -I {} sh -c 'if [ -f "{}" ] && [ $(stat -f%z "{}" 2>/dev/null || stat -c%s "{}" 2>/dev/null || echo 0) -gt 10485760 ]; then echo "{}"; fi' || true)
if [ ! -z "$LARGE_FILES" ]; then
    echo -e "${YELLOW}⚠️  警告：检测到大文件（>10MB）：${NC}"
    echo "$LARGE_FILES"
    echo "请考虑使用 Git LFS 或将文件添加到 .gitignore"
fi

# 检查是否有 TODO 或 FIXME 注释包含敏感信息
echo "检查注释中的敏感信息..."
SENSITIVE_COMMENTS=$(git diff --cached | grep -iE "(TODO|FIXME|XXX).*(password|key|secret|token)" || true)
if [ ! -z "$SENSITIVE_COMMENTS" ]; then
    echo -e "${YELLOW}⚠️  警告：注释中可能包含敏感信息：${NC}"
    echo "$SENSITIVE_COMMENTS"
    echo "请检查并移除敏感信息"
fi

# 检查是否有调试信息或日志输出包含敏感数据
echo "检查调试输出..."
DEBUG_PATTERNS=(
    "console\.log.*password"
    "console\.log.*key"
    "console\.log.*secret"
    "console\.log.*token"
    "print.*password"
    "print.*key"
    "print.*secret"
    "print.*token"
)

for pattern in "${DEBUG_PATTERNS[@]}"; do
    MATCHES=$(git diff --cached | grep -iE "$pattern" || true)
    if [ ! -z "$MATCHES" ]; then
        echo -e "${YELLOW}⚠️  警告：检测到可能泄露敏感信息的调试输出：${NC}"
        echo "$MATCHES"
        echo "请移除或修改调试输出"
    fi
done

# 检查提交信息
if [ -f ".git/COMMIT_EDITMSG" ]; then
    echo "检查提交信息..."
    COMMIT_MSG=$(cat .git/COMMIT_EDITMSG || true)
    SENSITIVE_IN_MSG=$(echo "$COMMIT_MSG" | grep -iE "(password|key|secret|token)" || true)
    if [ ! -z "$SENSITIVE_IN_MSG" ]; then
        echo -e "${YELLOW}⚠️  警告：提交信息中可能包含敏感词汇${NC}"
        echo "请检查提交信息是否包含敏感信息"
    fi
fi

echo -e "${GREEN}✅ 安全检查完成${NC}"
echo ""

# 如果有警告，询问用户是否继续
if [ ! -z "$ENV_FILES" ] || [ ! -z "$LARGE_FILES" ] || [ ! -z "$SENSITIVE_COMMENTS" ]; then
    echo -e "${YELLOW}发现一些警告项目。是否继续提交？ (y/N)${NC}"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo -e "${RED}提交已取消${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}🚀 安全检查通过，继续提交...${NC}"
exit 0
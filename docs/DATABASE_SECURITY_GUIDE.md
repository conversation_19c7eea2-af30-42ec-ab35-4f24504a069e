# 数据库安全配置指南

## 概述

为了提高安全性，我们已将数据库密码从 `DATABASE_URL` 中移除，改为使用单独的环境变量管理。

## 配置说明

### 环境变量配置

```bash
# 数据库连接 URL（不包含密码）
DATABASE_URL=postgresql://yoghurt_user@localhost:6432/yoghurt_qc

# 单独的数据库凭据
DB_HOST=localhost
DB_PORT=6432
DB_NAME=yoghurt_qc
DB_USER=yoghurt_user
DB_PASSWORD=your_actual_secure_password_here
DB_SSL=false
```

### 应用程序中的使用方法

#### 方法 1：构建完整的连接字符串

```typescript
// backend/src/config/database.ts
const buildDatabaseUrl = (): string => {
  const { DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD, DB_SSL } = process.env;
  
  const sslParam = DB_SSL === 'true' ? '?sslmode=require' : '';
  
  return `postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}${sslParam}`;
};

export const DATABASE_URL = buildDatabaseUrl();
```

#### 方法 2：使用连接配置对象

```typescript
// backend/src/config/database.ts
export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '6432'),
  database: process.env.DB_NAME || 'yoghurt_qc',
  username: process.env.DB_USER || 'yoghurt_user',
  password: process.env.DB_PASSWORD,
  ssl: process.env.DB_SSL === 'true'
};
```

#### 方法 3：TypeORM 配置示例

```typescript
// backend/src/config/typeorm.config.ts
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const typeOrmConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '6432'),
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  ssl: process.env.DB_SSL === 'true',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development'
};
```

## 安全最佳实践

### 1. 环境变量管理

- **开发环境**：使用 `.env.local` 文件存储真实密码
- **生产环境**：使用密钥管理服务（如 AWS Secrets Manager、Azure Key Vault）
- **CI/CD**：使用加密的环境变量或密钥存储

### 2. 密码要求

- 最少 12 个字符
- 包含大小写字母、数字和特殊字符
- 避免使用常见词汇或个人信息
- 定期轮换密码

### 3. 连接安全

```bash
# 生产环境建议配置
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=true
```

### 4. 监控和审计

- 启用数据库连接日志
- 监控异常连接尝试
- 定期审查数据库访问权限

## 迁移指南

### 从旧配置迁移

如果您之前使用包含密码的 `DATABASE_URL`：

1. **备份当前配置**
   ```bash
   cp .env .env.backup
   ```

2. **更新环境变量**
   - 移除 `DATABASE_URL` 中的密码部分
   - 确保 `DB_PASSWORD` 包含正确的密码

3. **更新应用程序代码**
   - 使用上述方法之一重新构建连接字符串
   - 测试数据库连接

4. **验证配置**
   ```bash
   npm run test:db-connection
   ```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查 `DB_HOST` 和 `DB_PORT` 是否正确
   - 确认数据库服务正在运行

2. **认证失败**
   - 验证 `DB_USER` 和 `DB_PASSWORD` 是否正确
   - 检查数据库用户权限

3. **SSL 连接问题**
   - 确认 `DB_SSL` 设置与数据库服务器配置匹配
   - 检查 SSL 证书配置

### 调试命令

```bash
# 测试数据库连接
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME

# 检查环境变量
echo "Host: $DB_HOST"
echo "Port: $DB_PORT"
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo "Password: [HIDDEN]"
```

## 相关文档

- [安全指南](../SECURITY_GUIDE.md)
- [环境配置](../GETTING_STARTED.md)
- [部署指南](../docs/tech/Infrastructure_as_Code.md)
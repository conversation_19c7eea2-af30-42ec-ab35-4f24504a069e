# Docker Compose Security Guide

## Overview

This guide explains the security improvements made to the Docker Compose configuration and how to properly manage secrets in containerized environments.

## Security Issues Fixed

### 1. Hardcoded Database Password
- **Issue**: PostgreSQL password was hardcoded in `docker-compose.yml`
- **Fix**: Replaced with environment variable `${DB_PASSWORD}`
- **Impact**: Prevents password exposure in version control

### 2. Hardcoded JWT Secret
- **Issue**: JWT secret key was hardcoded as placeholder text
- **Fix**: Replaced with environment variable `${JWT_SECRET}`
- **Impact**: Enables proper JWT token security

## Environment Variable Configuration

### Required Environment Variables

The following environment variables must be set before running Docker Compose:

```bash
# Database password (used by postgres and backend services)
DB_PASSWORD=your_secure_database_password

# JWT secret key (minimum 32 characters)
JWT_SECRET=your_secure_jwt_secret_key_at_least_32_chars

# OpenAI API key (for AI service)
OPENAI_API_KEY=your_openai_api_key
```

### Setup Methods

#### Method 1: Environment File (Recommended)

1. **Copy the template**:
   ```bash
   cp .env.docker .env.docker.local
   ```

2. **Edit the local file**:
   ```bash
   # Edit .env.docker.local with your actual values
   nano .env.docker.local
   ```

3. **Load environment variables**:
   ```bash
   # Load variables before running Docker Compose
   export $(cat .env.docker.local | xargs)
   docker-compose up
   ```

#### Method 2: Direct Export

```bash
# Export variables directly
export DB_PASSWORD="your_secure_password"
export JWT_SECRET="your_secure_jwt_secret_key_at_least_32_characters"
export OPENAI_API_KEY="your_openai_api_key"

# Run Docker Compose
docker-compose up
```

#### Method 3: .env File (Docker Compose Auto-load)

Create a `.env` file in the project root (this will be auto-loaded by Docker Compose):

```bash
# .env file (auto-loaded by docker-compose)
DB_PASSWORD=your_secure_password
JWT_SECRET=your_secure_jwt_secret_key
OPENAI_API_KEY=your_openai_api_key
```

**⚠️ Warning**: Ensure `.env` is in `.gitignore` to prevent committing secrets.

## Security Best Practices

### 1. Password Generation

```bash
# Generate a secure database password
openssl rand -base64 32

# Generate a secure JWT secret
openssl rand -base64 64
```

### 2. Environment Validation

```bash
# Verify your configuration without exposing secrets
docker-compose config | grep -E "(DB_PASSWORD|JWT_SECRET|OPENAI_API_KEY)" | sed 's/:.*/: [HIDDEN]/' 

# Check if all required variables are set
echo "DB_PASSWORD: ${DB_PASSWORD:+SET}"
echo "JWT_SECRET: ${JWT_SECRET:+SET}"
echo "OPENAI_API_KEY: ${OPENAI_API_KEY:+SET}"
```

### 3. Production Considerations

#### Docker Secrets (Docker Swarm)

For production deployments, consider using Docker Secrets:

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  postgres:
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
    secrets:
      - db_password

secrets:
  db_password:
    external: true
```

#### External Secret Management

- **HashiCorp Vault**: Integrate with Vault for dynamic secrets
- **AWS Secrets Manager**: Use AWS CLI or SDK to fetch secrets
- **Azure Key Vault**: Integrate with Azure Key Vault
- **Kubernetes Secrets**: Use K8s secrets for container orchestration

### 4. Development vs Production

#### Development Environment
```bash
# Use simple environment variables for development
export DB_PASSWORD="dev_password_123"
export JWT_SECRET="dev_jwt_secret_key_for_development_only"
```

#### Production Environment
```bash
# Use strong, unique secrets for production
export DB_PASSWORD="$(openssl rand -base64 32)"
export JWT_SECRET="$(openssl rand -base64 64)"
```

## Troubleshooting

### Common Issues

1. **Environment variables not set**
   ```bash
   # Error: WARNING: The DB_PASSWORD variable is not set
   # Solution: Set the required environment variables
   export DB_PASSWORD="your_password"
   ```

2. **Database connection failed**
   ```bash
   # Check if password matches between services
   docker-compose logs postgres
   docker-compose logs backend
   ```

3. **JWT token issues**
   ```bash
   # Ensure JWT_SECRET is at least 32 characters
   echo ${#JWT_SECRET}  # Should output >= 32
   ```

### Debugging Commands

```bash
# Check environment variable substitution
docker-compose config

# View service logs
docker-compose logs postgres
docker-compose logs backend
docker-compose logs ai-service

# Test database connection
docker-compose exec postgres psql -U yoghurt_user -d yoghurt_qc -c "SELECT version();"

# Test backend health
curl http://localhost:6000/health
```

## Security Checklist

- [ ] All hardcoded secrets removed from `docker-compose.yml`
- [ ] Environment variables properly set
- [ ] `.env.docker.local` added to `.gitignore`
- [ ] Strong passwords generated (minimum 12 characters)
- [ ] JWT secret is cryptographically secure (minimum 32 characters)
- [ ] API keys are valid and have appropriate permissions
- [ ] Production secrets are different from development
- [ ] Secret rotation schedule established
- [ ] Access to secrets is properly restricted
- [ ] Monitoring and alerting configured for secret access

## Related Documentation

- [Database Security Guide](./DATABASE_SECURITY_GUIDE.md)
- [Security Architecture](./tech/Security_Architecture.md)
- [Infrastructure as Code](./tech/Infrastructure_as_Code.md)
- [Getting Started Guide](../GETTING_STARTED.md)

## Emergency Procedures

### Secret Compromise Response

1. **Immediate Actions**:
   ```bash
   # Stop all services
   docker-compose down
   
   # Generate new secrets
   export NEW_DB_PASSWORD="$(openssl rand -base64 32)"
   export NEW_JWT_SECRET="$(openssl rand -base64 64)"
   ```

2. **Update Database Password**:
   ```bash
   # Connect to database and change password
   docker-compose exec postgres psql -U postgres -c "ALTER USER yoghurt_user PASSWORD '$NEW_DB_PASSWORD';"
   ```

3. **Restart Services**:
   ```bash
   # Update environment variables and restart
   export DB_PASSWORD="$NEW_DB_PASSWORD"
   export JWT_SECRET="$NEW_JWT_SECRET"
   docker-compose up -d
   ```

4. **Invalidate Sessions**:
   - All existing JWT tokens will be invalidated due to secret change
   - Users will need to re-authenticate
   - Clear Redis cache if needed: `docker-compose exec redis redis-cli FLUSHALL`
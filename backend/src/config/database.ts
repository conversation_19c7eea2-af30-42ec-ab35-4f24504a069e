import { Pool } from 'pg'
import { config } from './index'
import { logger } from '../utils/logger'

let pool: Pool | null = null

export const connectDatabase = async (): Promise<Pool> => {
  if (pool) {
    return pool
  }

  try {
    // 调试信息
    logger.info('Database config:', {
      host: config.database.host,
      port: config.database.port,
      database: config.database.name,
      user: config.database.user,
      passwordType: typeof config.database.password,
      passwordLength: config.database.password?.length || 0,
    })

    pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.name,
      user: config.database.user,
      password: String(config.database.password), // 确保密码是字符串
      ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
      min: config.database.poolMin,
      max: config.database.poolMax,
      idleTimeoutMillis: config.database.poolIdleTimeout,
      connectionTimeoutMillis: 10000,
    })

    // 测试连接
    const client = await pool.connect()
    await client.query('SELECT NOW()')
    client.release()

    logger.info('Database connection established successfully')
    
    // 监听连接事件
    pool.on('connect', () => {
      logger.debug('New database client connected')
    })

    pool.on('error', (err) => {
      logger.error('Database pool error:', err)
    })

    return pool
  } catch (error) {
    logger.error('Failed to connect to database:', error)
    throw error
  }
}

export const getDatabase = (): Pool => {
  if (!pool) {
    throw new Error('Database not connected. Call connectDatabase() first.')
  }
  return pool
}

export const closeDatabase = async (): Promise<void> => {
  if (pool) {
    await pool.end()
    pool = null
    logger.info('Database connection closed')
  }
}

// 健康检查
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    if (!pool) {
      return false
    }
    
    const client = await pool.connect()
    await client.query('SELECT 1')
    client.release()
    return true
  } catch (error) {
    logger.error('Database health check failed:', error)
    return false
  }
}

export default {
  connectDatabase,
  getDatabase,
  closeDatabase,
  checkDatabaseHealth,
}

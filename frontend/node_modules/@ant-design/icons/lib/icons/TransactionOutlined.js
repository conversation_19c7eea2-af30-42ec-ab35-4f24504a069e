"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _TransactionOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/TransactionOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const TransactionOutlined = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _TransactionOutlined.default
}));

/**![transaction](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY2OC42IDMyMGMwLTQuNC0zLjYtOC04LThoLTU0LjVjLTMgMC01LjggMS43LTcuMSA0LjRsLTg0LjcgMTY4LjhINTExbC04NC43LTE2OC44YTggOCAwIDAwLTcuMS00LjRoLTU1LjdjLTEuMyAwLTIuNi4zLTMuOCAxLTMuOSAyLjEtNS4zIDctMy4yIDEwLjhsMTAzLjkgMTkxLjZoLTU3Yy00LjQgMC04IDMuNi04IDh2MjcuMWMwIDQuNCAzLjYgOCA4IDhoNzZ2MzloLTc2Yy00LjQgMC04IDMuNi04IDh2MjcuMWMwIDQuNCAzLjYgOCA4IDhoNzZWNzA0YzAgNC40IDMuNiA4IDggOGg0OS45YzQuNCAwIDgtMy42IDgtOHYtNjMuNWg3Ni4zYzQuNCAwIDgtMy42IDgtOHYtMjcuMWMwLTQuNC0zLjYtOC04LThoLTc2LjN2LTM5aDc2LjNjNC40IDAgOC0zLjYgOC04di0yNy4xYzAtNC40LTMuNi04LTgtOEg1NjRsMTAzLjctMTkxLjZjLjUtMS4xLjktMi40LjktMy43ek0xNTcuOSA1MDQuMmEzNTIuNyAzNTIuNyAwIDAxMTAzLjUtMjQyLjRjMzIuNS0zMi41IDcwLjMtNTguMSAxMTIuNC03NS45IDQzLjYtMTguNCA4OS45LTI3LjggMTM3LjYtMjcuOCA0Ny44IDAgOTQuMSA5LjMgMTM3LjYgMjcuOCA0Mi4xIDE3LjggNzkuOSA0My40IDExMi40IDc1LjkgMTAgMTAgMTkuMyAyMC41IDI3LjkgMzEuNGwtNTAgMzkuMWE4IDggMCAwMDMgMTQuMWwxNTYuOCAzOC4zYzUgMS4yIDkuOS0yLjYgOS45LTcuN2wuOC0xNjEuNWMwLTYuNy03LjctMTAuNS0xMi45LTYuM2wtNDcuOCAzNy40Qzc3MC43IDE0Ni4zIDY0OC42IDgyIDUxMS41IDgyIDI3NyA4MiA4Ni4zIDI3MC4xIDgyIDUwMy44YTggOCAwIDAwOCA4LjJoNjBjNC4zIDAgNy44LTMuNSA3LjktNy44ek05MzQgNTEyaC02MGMtNC4zIDAtNy45IDMuNS04IDcuOGEzNTIuNyAzNTIuNyAwIDAxLTEwMy41IDI0Mi40IDM1Mi41NyAzNTIuNTcgMCAwMS0xMTIuNCA3NS45Yy00My42IDE4LjQtODkuOSAyNy44LTEzNy42IDI3LjhzLTk0LjEtOS4zLTEzNy42LTI3LjhhMzUyLjU3IDM1Mi41NyAwIDAxLTExMi40LTc1LjljLTEwLTEwLTE5LjMtMjAuNS0yNy45LTMxLjRsNDkuOS0zOS4xYTggOCAwIDAwLTMtMTQuMWwtMTU2LjgtMzguM2MtNS0xLjItOS45IDIuNi05LjkgNy43bC0uOCAxNjEuN2MwIDYuNyA3LjcgMTAuNSAxMi45IDYuM2w0Ny44LTM3LjRDMjUzLjMgODc3LjcgMzc1LjQgOTQyIDUxMi41IDk0MiA3NDcgOTQyIDkzNy43IDc1My45IDk0MiA1MjAuMmE4IDggMCAwMC04LTguMnoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(TransactionOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TransactionOutlined';
}
var _default = exports.default = RefIcon;
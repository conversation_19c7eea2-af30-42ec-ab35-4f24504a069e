"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _StarTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/StarTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const StarTwoTone = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _StarTwoTone.default
}));

/**![star](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi41IDE5MC40bC05NC40IDE5MS4zLTIxMS4yIDMwLjcgMTUyLjggMTQ5LTM2LjEgMjEwLjMgMTg4LjktOTkuMyAxODguOSA5OS4yLTM2LjEtMjEwLjMgMTUyLjgtMTQ4LjktMjExLjItMzAuN3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTkwOC42IDM1Mi44bC0yNTMuOS0zNi45TDU0MS4yIDg1LjhjLTMuMS02LjMtOC4yLTExLjQtMTQuNS0xNC41LTE1LjgtNy44LTM1LTEuMy00Mi45IDE0LjVMMzcwLjMgMzE1LjlsLTI1My45IDM2LjljLTcgMS0xMy40IDQuMy0xOC4zIDkuM2EzMi4wNSAzMi4wNSAwIDAwLjYgNDUuM2wxODMuNyAxNzkuMUwyMzkgODM5LjRhMzEuOTUgMzEuOTUgMCAwMDQ2LjQgMzMuN2wyMjcuMS0xMTkuNCAyMjcuMSAxMTkuNGM2LjIgMy4zIDEzLjQgNC40IDIwLjMgMy4yIDE3LjQtMyAyOS4xLTE5LjUgMjYuMS0zNi45bC00My40LTI1Mi45IDE4My43LTE3OS4xYzUtNC45IDguMy0xMS4zIDkuMy0xOC4zIDIuNy0xNy41LTkuNS0zMy43LTI3LTM2LjN6TTY2NS4zIDU2MS4zbDM2LjEgMjEwLjMtMTg4LjktOTkuMi0xODguOSA5OS4zIDM2LjEtMjEwLjMtMTUyLjgtMTQ5IDIxMS4yLTMwLjcgOTQuNC0xOTEuMyA5NC40IDE5MS4zIDIxMS4yIDMwLjctMTUyLjggMTQ4Ljl6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(StarTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StarTwoTone';
}
var _default = exports.default = RefIcon;
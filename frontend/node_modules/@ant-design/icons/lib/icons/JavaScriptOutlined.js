"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _JavaScriptOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/JavaScriptOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const JavaScriptOutlined = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _JavaScriptOutlined.default
}));

/**![java-script](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNDE2IDE3NkgyNTUuNTR2NDI1LjYyYzAgMTA1LjMtMzYuMTYgMTM0LjcxLTk5LjEgMTM0LjcxLTI5LjUgMC01Ni4wNS01LjA1LTc2LjcyLTEyLjE0TDYzIDg0OC43OUM5Mi40OCA4NTguOTEgMTM3LjczIDg2NSAxNzMuMTMgODY1IDMxNy42MyA4NjUgNDE2IDc5Ny4xNiA0MTYgNjAyLjY2em0zNDkuNDktMTZDNjEwLjI2IDE2MCA1MTIgMjQ4LjEzIDUxMiAzNjQuNmMwIDEwMC4zMiA3NS42NyAxNjMuMTMgMTg1LjcgMjAzLjY0IDc5LjU3IDI4LjM2IDExMS4wMyA1My43IDExMS4wMyA5NS4yMiAwIDQ1LjU3LTM2LjM2IDc0Ljk2LTEwNS4xMyA3NC45Ni02My44NyAwLTEyMS44NS0yMS4zMS0xNjEuMTUtNDIuNTh2LS4wNEw1MTIgODIyLjQzQzU0OS4zNiA4NDMuNzMgNjE5LjEyIDg2NSA2OTQuNzQgODY1IDg3Ni41MiA4NjUgOTYxIDc2Ny43NSA5NjEgNjUzLjNjMC05Ny4yNS01NC4wNC0xNjAuMDQtMTcwLjk0LTIwNC42My04Ni40Ny0zNC40NC0xMjIuODEtNTMuNjctMTIyLjgxLTk3LjIzIDAtMzQuNDUgMzEuNDUtNjUuODQgOTYuMy02NS44NCA2My44MyAwIDEwNy43MyAyMS40NSAxMzMuMyAzNC42NGwzOC4zNC0xMjguMTlDODk1LjEgMTc0LjQ2IDg0MS4xMSAxNjAgNzY1LjUgMTYwIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(JavaScriptOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'JavaScriptOutlined';
}
var _default = exports.default = RefIcon;
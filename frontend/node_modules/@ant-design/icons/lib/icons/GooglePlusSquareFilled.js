"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _GooglePlusSquareFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/GooglePlusSquareFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const GooglePlusSquareFilled = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _GooglePlusSquareFilled.default
}));

/**![google-plus-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTQ4LjUgNjIyLjhjLTQzLjkgNjEuOC0xMzIuMSA3OS44LTIwMC45IDUzLjMtNjktMjYuMy0xMTgtOTkuMi0xMTIuMS0xNzMuNSAxLjUtOTAuOSA4NS4yLTE3MC42IDE3Ni4xLTE2Ny41IDQzLjYtMiA4NC42IDE2LjkgMTE4IDQzLjYtMTQuMyAxNi4yLTI5IDMxLjgtNDQuOCA0Ni4zLTQwLjEtMjcuNy05Ny4yLTM1LjYtMTM3LjMtMy42LTU3LjQgMzkuNy02MCAxMzMuNC00LjggMTc2LjEgNTMuNyA0OC43IDE1NS4yIDI0LjUgMTcwLjEtNTAuMS0zMy42LS41LTY3LjQgMC0xMDEtMS4xLS4xLTIwLjEtLjItNDAuMS0uMS02MC4yIDU2LjItLjIgMTEyLjUtLjMgMTY4LjguMiAzLjMgNDcuMy0zIDk3LjUtMzIgMTM2LjV6TTc5MSA1MzYuNWMtMTYuOC4yLTMzLjYuMy01MC40LjQtLjIgMTYuOC0uMyAzMy42LS4zIDUwLjRINjkwYy0uMi0xNi44LS4yLTMzLjUtLjMtNTAuMy0xNi44LS4yLTMzLjYtLjMtNTAuNC0uNXYtNTAuMWMxNi44LS4yIDMzLjYtLjMgNTAuNC0uMy4xLTE2LjguMy0zMy42LjQtNTAuNGg1MC4ybC4zIDUwLjRjMTYuOC4yIDMzLjYuMiA1MC40LjN2NTAuMXoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(GooglePlusSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GooglePlusSquareFilled';
}
var _default = exports.default = RefIcon;
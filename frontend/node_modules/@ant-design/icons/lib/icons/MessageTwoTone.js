"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _MessageTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/MessageTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const MessageTwoTone = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _MessageTwoTone.default
}));

/**![message](data:image/svg+xml;base64,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) */
const RefIcon = /*#__PURE__*/React.forwardRef(MessageTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MessageTwoTone';
}
var _default = exports.default = RefIcon;
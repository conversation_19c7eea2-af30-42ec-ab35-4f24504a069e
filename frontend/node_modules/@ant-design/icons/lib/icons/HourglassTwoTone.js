"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _HourglassTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/HourglassTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const HourglassTwoTone = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _HourglassTwoTone.default
}));

/**![hourglass](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA1NDhjLTQyLjIgMC04MS45IDE2LjQtMTExLjcgNDYuM0ExNTYuNjMgMTU2LjYzIDAgMDAzNTQgNzA2djEzNGgzMTZWNzA2YzAtNDIuMi0xNi40LTgxLjktNDYuMy0xMTEuN0ExNTYuNjMgMTU2LjYzIDAgMDA1MTIgNTQ4ek0zNTQgMzE4YzAgNDIuMiAxNi40IDgxLjkgNDYuMyAxMTEuN0M0MzAuMSA0NTkuNiA0NjkuOCA0NzYgNTEyIDQ3NnM4MS45LTE2LjQgMTExLjctNDYuM0M2NTMuNiAzOTkuOSA2NzAgMzYwLjIgNjcwIDMxOFYxODRIMzU0djEzNHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTc0MiAzMThWMTg0aDg2YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDE5NmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg4NnYxMzRjMCA4MS41IDQyLjQgMTUzLjIgMTA2LjQgMTk0LTY0IDQwLjgtMTA2LjQgMTEyLjUtMTA2LjQgMTk0djEzNGgtODZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNjMyYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04aC04NlY3MDZjMC04MS41LTQyLjQtMTUzLjItMTA2LjQtMTk0IDY0LTQwLjggMTA2LjQtMTEyLjUgMTA2LjQtMTk0em0tNzIgMzg4djEzNEgzNTRWNzA2YzAtNDIuMiAxNi40LTgxLjkgNDYuMy0xMTEuN0M0MzAuMSA1NjQuNCA0NjkuOCA1NDggNTEyIDU0OHM4MS45IDE2LjQgMTExLjcgNDYuM0M2NTMuNiA2MjQuMSA2NzAgNjYzLjggNjcwIDcwNnptMC0zODhjMCA0Mi4yLTE2LjQgODEuOS00Ni4zIDExMS43QzU5My45IDQ1OS42IDU1NC4yIDQ3NiA1MTIgNDc2cy04MS45LTE2LjQtMTExLjctNDYuM0ExNTYuNjMgMTU2LjYzIDAgMDEzNTQgMzE4VjE4NGgzMTZ2MTM0eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(HourglassTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HourglassTwoTone';
}
var _default = exports.default = RefIcon;
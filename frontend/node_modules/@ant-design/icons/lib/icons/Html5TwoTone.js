"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _Html5TwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/Html5TwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const Html5TwoTone = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _Html5TwoTone.default
}));

/**![html5](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0NSA5Nmw2NiA3NDYuNkw1MTEuOCA5MjhsMjk5LjYtODUuNEw4NzguNyA5NkgxNDV6bTYxMC45IDcwMC42bC0yNDQuMSA2OS42LTI0NS4yLTY5LjYtNTYuNy02NDEuMmg2MDMuOGwtNTcuOCA2NDEuMnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTIwOS45IDE1NS40bDU2LjcgNjQxLjIgMjQ1LjIgNjkuNiAyNDQuMS02OS42IDU3LjgtNjQxLjJIMjA5Ljl6bTUzMC40IDExNy45bC00LjggNDcuMi0xLjcgMTkuNUgzODEuN2w4LjIgOTQuMkg1MTF2LS4yaDIxNC43bC0zLjIgMjQuMy0yMS4yIDI0Mi4yLTEuNyAxNi4zLTE4Ny43IDUxLjd2LjRoLTEuN2wtMTg4LjYtNTItMTEuMy0xNDQuN2g5MWw2LjUgNzMuMiAxMDIuNCAyNy43aC44di0uMmwxMDIuNC0yNy43IDExLjQtMTE4LjVINTExLjl2LjFIMzA1LjRsLTIyLjctMjUzLjVMMjgxIDI0OWg0NjFsLTEuNyAyNC4zeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNMjgxIDI0OWwxLjcgMjQuMyAyMi43IDI1My41aDIwNi41di0uMWgxMTIuOWwtMTEuNCAxMTguNUw1MTEgNjcyLjl2LjJoLS44bC0xMDIuNC0yNy43LTYuNS03My4yaC05MWwxMS4zIDE0NC43IDE4OC42IDUyaDEuN3YtLjRsMTg3LjctNTEuNyAxLjctMTYuMyAyMS4yLTI0Mi4yIDMuMi0yNC4zSDUxMXYuMkgzODkuOWwtOC4yLTk0LjJoMzUyLjFsMS43LTE5LjUgNC44LTQ3LjJMNzQyIDI0OUg1MTF6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(Html5TwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'Html5TwoTone';
}
var _default = exports.default = RefIcon;
/* =============================================================================
   Layout 组件样式
   ============================================================================= */

.layout-container {
  height: 100vh;
  overflow: hidden;
}

/* 侧边栏样式 */
.layout-sider {
  background: #ffffff !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  border-right: 1px solid #f0f0f0;
  position: relative;
  z-index: 10;
}

.layout-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Logo区域 */
.layout-logo {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #ffffff;
  min-height: 64px;
}

.logo-icon {
  font-size: 32px;
  margin-right: 12px;
  flex-shrink: 0;
}

.logo-text {
  flex: 1;
  min-width: 0;
}

.logo-title {
  font-size: 16px;
  font-weight: 600;
  color: #F05654;
  line-height: 1.2;
  margin-bottom: 2px;
}

.logo-subtitle {
  font-size: 12px;
  color: #A8ABB0;
  line-height: 1;
}

/* 菜单样式 */
.layout-menu {
  flex: 1;
  border-right: none !important;
  background: transparent !important;
  padding: 8px 0;
}

.layout-menu .ant-menu-item {
  margin: 4px 12px;
  border-radius: 8px;
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
}

.layout-menu .ant-menu-item-selected {
  background-color: rgba(240, 86, 84, 0.1) !important;
  color: #F05654 !important;
}

.layout-menu .ant-menu-item-selected::after {
  display: none;
}

.layout-menu .ant-menu-item:hover {
  background-color: rgba(240, 86, 84, 0.05) !important;
  color: #F05654 !important;
}

.layout-menu .ant-menu-item .anticon {
  font-size: 16px;
  margin-right: 12px;
}

/* 主内容区域 */
.layout-main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* 顶部导航栏 */
.layout-header {
  background: #ffffff !important;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  position: relative;
  z-index: 9;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-trigger {
  font-size: 18px;
  color: #666;
  transition: color 0.3s;
}

.collapse-trigger:hover {
  color: #F05654 !important;
  background-color: rgba(240, 86, 84, 0.1) !important;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-action {
  color: #666;
  font-size: 16px;
  transition: all 0.3s;
}

.header-action:hover {
  color: #F05654 !important;
  background-color: rgba(240, 86, 84, 0.1) !important;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: rgba(240, 86, 84, 0.1);
}

.user-name {
  margin-left: 8px;
  font-weight: 500;
  color: #262626;
}

/* 内容区域 */
.layout-content {
  flex: 1;
  overflow: hidden;
  background: #fafafa;
}

.content-wrapper {
  height: 100%;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .user-name {
    display: none;
  }
  
  .layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 1000;
  }
  
  .layout-main {
    margin-left: 0 !important;
  }
}

/* 折叠状态下的样式调整 */
.ant-layout-sider-collapsed .layout-logo {
  padding: 16px 12px;
  justify-content: center;
}

.ant-layout-sider-collapsed .logo-text {
  display: none;
}

.ant-layout-sider-collapsed .layout-menu .ant-menu-item {
  margin: 4px 8px;
  justify-content: center;
}

.ant-layout-sider-collapsed .layout-menu .ant-menu-item .anticon {
  margin-right: 0;
}

/* 滚动条样式 */
.content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.content-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb {
  background: #A8ABB0;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #F05654;
}

/* 动画效果 */
.layout-sider {
  transition: all 0.2s;
}

.layout-menu .ant-menu-item {
  transition: all 0.3s;
}

.user-info {
  transition: all 0.3s;
}

/* 通知徽章样式 */
.ant-badge-count {
  background-color: #F05654 !important;
  border-color: #F05654 !important;
}

/* 下拉菜单样式 */
.ant-dropdown-menu-item:hover {
  background-color: rgba(240, 86, 84, 0.1) !important;
  color: #F05654 !important;
}

.ant-dropdown-menu-item-selected {
  background-color: rgba(240, 86, 84, 0.1) !important;
  color: #F05654 !important;
}

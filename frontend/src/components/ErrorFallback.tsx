import React from 'react'
import { Result, Button, Typography } from 'antd'
import { FallbackProps } from 'react-error-boundary'

const { Paragraph, Text } = Typography

const ErrorFallback: React.FC<FallbackProps> = ({ error, resetErrorBoundary }) => {
  return (
    <div className="full-height flex-center">
      <Result
        status="500"
        title="应用程序出现错误"
        subTitle="抱歉，应用程序遇到了意外错误。请尝试刷新页面或联系技术支持。"
        extra={
          <div style={{ textAlign: 'center' }}>
            <Button 
              type="primary" 
              onClick={resetErrorBoundary}
              style={{ marginRight: 16 }}
            >
              重试
            </Button>
            <Button onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          </div>
        }
      >
        {process.env.NODE_ENV === 'development' && (
          <div style={{ marginTop: 24, textAlign: 'left' }}>
            <Typography.Title level={5} style={{ color: '#ff4d4f' }}>
              错误详情 (开发模式)：
            </Typography.Title>
            <Paragraph>
              <Text code style={{ whiteSpace: 'pre-wrap', fontSize: 12 }}>
                {error.message}
              </Text>
            </Paragraph>
            {error.stack && (
              <Paragraph>
                <Text code style={{ whiteSpace: 'pre-wrap', fontSize: 10 }}>
                  {error.stack}
                </Text>
              </Paragraph>
            )}
          </div>
        )}
      </Result>
    </div>
  )
}

export default ErrorFallback

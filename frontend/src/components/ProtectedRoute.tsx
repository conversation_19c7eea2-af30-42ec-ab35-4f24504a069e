import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { RootState } from '../store'
import LoadingSpinner from './LoadingSpinner'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRoles?: string[]
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRoles = [] 
}) => {
  const location = useLocation()
  const { isAuthenticated, isLoading, user } = useSelector((state: RootState) => state.auth)

  // 如果正在加载认证状态，显示加载器
  if (isLoading) {
    return <LoadingSpinner tip="验证用户身份..." />
  }

  // 如果未认证，重定向到登录页面
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // 如果需要特定角色权限
  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.includes(user.role)
    if (!hasRequiredRole) {
      return (
        <div className="full-height flex-center">
          <div style={{ textAlign: 'center' }}>
            <h3>权限不足</h3>
            <p>您没有访问此页面的权限</p>
          </div>
        </div>
      )
    }
  }

  return <>{children}</>
}

export default ProtectedRoute

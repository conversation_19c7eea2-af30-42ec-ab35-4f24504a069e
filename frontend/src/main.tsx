import React from 'react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { <PERSON>rowser<PERSON>outer } from 'react-router-dom'
import { ConfigProvider } from 'antd'

import { ErrorBoundary } from 'react-error-boundary'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import App from './App'
import { store, persistor } from './store'
import ErrorFallback from './components/ErrorFallback'
import LoadingSpinner from './components/LoadingSpinner'
import './index.css'

// 设置 dayjs 中文语言
dayjs.locale('zh-cn')

// Ant Design 主题配置 - 银红色主题
const theme = {
  token: {
    colorPrimary: '#F05654', // 银红色主色调
    colorPrimaryHover: '#FF6B69', // 悬停时的颜色
    colorPrimaryActive: '#E04442', // 激活时的颜色
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#A8ABB0', // 浅灰色辅助色
    colorText: '#262626',
    colorTextSecondary: '#A8ABB0',
    colorBorder: '#A8ABB0',
    colorBorderSecondary: '#f0f0f0',
    borderRadius: 8,
    fontSize: 14,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 8,
      fontWeight: 500,
    },
    Card: {
      borderRadius: 12,
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
    },
    Input: {
      borderRadius: 8,
    },
    Select: {
      borderRadius: 8,
    },
    Menu: {
      itemSelectedBg: 'rgba(240, 86, 84, 0.1)',
      itemSelectedColor: '#F05654',
    },
    Layout: {
      siderBg: '#ffffff',
      headerBg: '#ffffff',
    },
    Table: {
      borderRadius: 8,
    },
  },
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Application Error:', error, errorInfo)
        // 这里可以添加错误上报逻辑
      }}
    >
      <Provider store={store}>
        <PersistGate loading={<LoadingSpinner />} persistor={persistor}>
          <BrowserRouter>
            <ConfigProvider locale={zhCN} theme={theme}>
              <App />
            </ConfigProvider>
          </BrowserRouter>
        </PersistGate>
      </Provider>
    </ErrorBoundary>
  </React.StrictMode>,
)

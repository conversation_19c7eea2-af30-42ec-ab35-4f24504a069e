import { apiClient, ApiResponse } from './index'
import { Recipe, CreateRecipeForm } from '../../types'

// 配方查询参数
export interface RecipeQueryParams {
  page?: number
  pageSize?: number
  search?: string
  isActive?: boolean
  userId?: string
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'version'
  sortOrder?: 'asc' | 'desc'
}

// 配方列表响应
export interface RecipeListResponse {
  recipes: Recipe[]
  pagination: {
    current: number
    pageSize: number
    total: number
  }
}

// 配方统计响应
export interface RecipeStatsResponse {
  totalRecipes: number
  activeRecipes: number
  totalBatches: number
  avgQualityScore: number
  popularRecipes: Array<{
    id: string
    name: string
    usageCount: number
    avgQualityScore: number
  }>
}

// 配方API类
export const recipeAPI = {
  // 获取配方列表
  getRecipes: (params?: RecipeQueryParams) => {
    return apiClient.get<ApiResponse<RecipeListResponse>>('/api/recipes', { params })
  },

  // 获取配方详情
  getRecipeById: (id: string) => {
    return apiClient.get<ApiResponse<{ recipe: Recipe }>>(`/api/recipes/${id}`)
  },

  // 创建配方
  createRecipe: (recipeData: CreateRecipeForm) => {
    return apiClient.post<ApiResponse<{ recipe: Recipe }>>('/api/recipes', recipeData)
  },

  // 更新配方
  updateRecipe: (id: string, recipeData: Partial<CreateRecipeForm>) => {
    return apiClient.put<ApiResponse<{ recipe: Recipe }>>(`/api/recipes/${id}`, recipeData)
  },

  // 删除配方
  deleteRecipe: (id: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/recipes/${id}`)
  },

  // 复制配方
  duplicateRecipe: (id: string) => {
    return apiClient.post<ApiResponse<{ recipe: Recipe }>>(`/api/recipes/${id}/duplicate`)
  },

  // 激活/停用配方
  toggleRecipeStatus: (id: string, isActive: boolean) => {
    return apiClient.patch<ApiResponse<{ recipe: Recipe }>>(`/api/recipes/${id}/status`, { isActive })
  },

  // 获取配方版本历史
  getRecipeVersions: (id: string) => {
    return apiClient.get<ApiResponse<{ versions: Recipe[] }>>(`/api/recipes/${id}/versions`)
  },

  // 恢复到指定版本
  restoreRecipeVersion: (id: string, version: number) => {
    return apiClient.post<ApiResponse<{ recipe: Recipe }>>(`/api/recipes/${id}/restore`, { version })
  },

  // 获取配方使用统计
  getRecipeStats: (id: string) => {
    return apiClient.get<ApiResponse<{
      totalBatches: number
      successfulBatches: number
      avgQualityScore: number
      recentBatches: Array<{
        id: string
        batchNumber: string
        status: string
        qualityScore?: number
        createdAt: string
      }>
    }>>(`/api/recipes/${id}/stats`)
  },

  // 获取配方总体统计
  getOverallStats: () => {
    return apiClient.get<ApiResponse<RecipeStatsResponse>>('/api/recipes/stats')
  },

  // 搜索配方
  searchRecipes: (query: string, filters?: {
    isActive?: boolean
    userId?: string
    tags?: string[]
  }) => {
    return apiClient.get<ApiResponse<{ recipes: Recipe[] }>>('/api/recipes/search', {
      params: { q: query, ...filters }
    })
  },

  // 获取推荐配方
  getRecommendedRecipes: (limit?: number) => {
    return apiClient.get<ApiResponse<{ recipes: Recipe[] }>>('/api/recipes/recommended', {
      params: { limit }
    })
  },

  // 导出配方
  exportRecipe: (id: string, format: 'json' | 'pdf' | 'excel') => {
    return apiClient.get(`/api/recipes/${id}/export`, {
      params: { format },
      responseType: 'blob'
    })
  },

  // 导入配方
  importRecipe: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    
    return apiClient.post<ApiResponse<{ recipe: Recipe }>>('/api/recipes/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 批量操作
  batchUpdateRecipes: (ids: string[], updates: Partial<CreateRecipeForm>) => {
    return apiClient.patch<ApiResponse<{ updatedCount: number }>>('/api/recipes/batch', {
      ids,
      updates
    })
  },

  // 批量删除
  batchDeleteRecipes: (ids: string[]) => {
    return apiClient.delete<ApiResponse<{ deletedCount: number }>>('/api/recipes/batch', {
      data: { ids }
    })
  },
}

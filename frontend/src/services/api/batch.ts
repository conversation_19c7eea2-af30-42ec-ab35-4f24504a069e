import { apiClient, ApiResponse } from './index'
import { <PERSON>ch, BatchStatus, CreateBatchForm, SensoryAssessmentForm } from '../../types'

// 批次查询参数
export interface BatchQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: BatchStatus
  recipeId?: string
  userId?: string
  dateRange?: [string, string]
  sortBy?: 'batchNumber' | 'productionDate' | 'status' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

// 批次列表响应
export interface BatchListResponse {
  batches: Batch[]
  pagination: {
    current: number
    pageSize: number
    total: number
  }
}

// 批次统计响应
export interface BatchStatsResponse {
  totalBatches: number
  completedBatches: number
  inProgressBatches: number
  failedBatches: number
  avgQualityScore: number
  statusDistribution: Array<{
    status: BatchStatus
    count: number
    percentage: number
  }>
}

// 批次API类
export const batchAPI = {
  // 获取批次列表
  getBatches: (params?: BatchQueryParams) => {
    return apiClient.get<ApiResponse<BatchListResponse>>('/api/batches', { params })
  },

  // 获取批次详情
  getBatchById: (id: string) => {
    return apiClient.get<ApiResponse<{ batch: Batch }>>(`/api/batches/${id}`)
  },

  // 创建批次
  createBatch: (batchData: CreateBatchForm) => {
    return apiClient.post<ApiResponse<{ batch: Batch }>>('/api/batches', batchData)
  },

  // 更新批次
  updateBatch: (id: string, batchData: Partial<CreateBatchForm>) => {
    return apiClient.put<ApiResponse<{ batch: Batch }>>(`/api/batches/${id}`, batchData)
  },

  // 更新批次状态
  updateBatchStatus: (id: string, status: BatchStatus) => {
    return apiClient.patch<ApiResponse<{ batch: Batch }>>(`/api/batches/${id}/status`, { status })
  },

  // 删除批次
  deleteBatch: (id: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/batches/${id}`)
  },

  // 提交感官评估
  submitSensoryAssessment: (batchId: string, assessment: SensoryAssessmentForm) => {
    return apiClient.post<ApiResponse<{ batch: Batch }>>(`/api/batches/${batchId}/sensory-assessment`, assessment)
  },

  // 获取感官评估历史
  getSensoryAssessmentHistory: (batchId: string) => {
    return apiClient.get<ApiResponse<{ assessments: any[] }>>(`/api/batches/${batchId}/sensory-assessments`)
  },

  // 更新生产参数
  updateProductionParams: (id: string, params: {
    actualFermentationDuration?: number
    actualTemperature?: number
    quantity?: number
    notes?: string
  }) => {
    return apiClient.patch<ApiResponse<{ batch: Batch }>>(`/api/batches/${id}/production-params`, params)
  },

  // 获取批次统计
  getBatchStats: (filters?: {
    dateRange?: [string, string]
    recipeId?: string
    status?: BatchStatus
  }) => {
    return apiClient.get<ApiResponse<BatchStatsResponse>>('/api/batches/stats', { params: filters })
  },

  // 获取批次时间线
  getBatchTimeline: (id: string) => {
    return apiClient.get<ApiResponse<{
      timeline: Array<{
        id: string
        type: 'status_change' | 'assessment' | 'analysis' | 'note'
        title: string
        description: string
        timestamp: string
        user?: {
          id: string
          name: string
        }
        data?: any
      }>
    }>>(`/api/batches/${id}/timeline`)
  },

  // 添加批次备注
  addBatchNote: (id: string, note: string) => {
    return apiClient.post<ApiResponse<{ batch: Batch }>>(`/api/batches/${id}/notes`, { note })
  },

  // 获取批次备注
  getBatchNotes: (id: string) => {
    return apiClient.get<ApiResponse<{
      notes: Array<{
        id: string
        content: string
        createdAt: string
        user: {
          id: string
          name: string
        }
      }>
    }>>(`/api/batches/${id}/notes`)
  },

  // 复制批次
  duplicateBatch: (id: string) => {
    return apiClient.post<ApiResponse<{ batch: Batch }>>(`/api/batches/${id}/duplicate`)
  },

  // 批量更新状态
  batchUpdateStatus: (ids: string[], status: BatchStatus) => {
    return apiClient.patch<ApiResponse<{ updatedCount: number }>>('/api/batches/batch-status', {
      ids,
      status
    })
  },

  // 导出批次数据
  exportBatches: (params: BatchQueryParams & { format: 'excel' | 'csv' | 'pdf' }) => {
    return apiClient.get('/api/batches/export', {
      params,
      responseType: 'blob'
    })
  },

  // 获取质量趋势
  getQualityTrend: (params: {
    dateRange?: [string, string]
    recipeId?: string
    interval?: 'day' | 'week' | 'month'
  }) => {
    return apiClient.get<ApiResponse<{
      trend: Array<{
        date: string
        avgQualityScore: number
        batchCount: number
        successRate: number
      }>
    }>>('/api/batches/quality-trend', { params })
  },

  // 获取批次对比数据
  compareBatches: (batchIds: string[]) => {
    return apiClient.post<ApiResponse<{
      comparison: Array<{
        batch: Batch
        metrics: {
          qualityScore?: number
          fermentationTime: number
          temperature: number
          yield: number
        }
      }>
    }>>('/api/batches/compare', { batchIds })
  },

  // 获取推荐的下一步操作
  getRecommendedActions: (id: string) => {
    return apiClient.get<ApiResponse<{
      recommendations: Array<{
        type: 'status_change' | 'analysis' | 'assessment' | 'adjustment'
        title: string
        description: string
        priority: 'high' | 'medium' | 'low'
        estimatedTime?: number
      }>
    }>>(`/api/batches/${id}/recommendations`)
  },
}

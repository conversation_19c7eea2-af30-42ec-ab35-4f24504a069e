import { apiClient, ApiResponse } from './index'
import { User } from '../../types'

// 用户查询参数
export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  role?: User['role']
  isActive?: boolean
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLoginAt'
  sortOrder?: 'asc' | 'desc'
}

// 用户创建请求
export interface CreateUserRequest {
  name: string
  email: string
  password: string
  role: User['role']
  isActive?: boolean
}

// 用户更新请求
export interface UpdateUserRequest {
  name?: string
  email?: string
  role?: User['role']
  isActive?: boolean
}

// 用户API类
export const userAPI = {
  // 获取用户列表
  getUsers: (params?: UserQueryParams) => {
    return apiClient.get<ApiResponse<{
      users: User[]
      pagination: {
        current: number
        pageSize: number
        total: number
      }
    }>>('/api/users', { params })
  },

  // 获取用户详情
  getUserById: (id: string) => {
    return apiClient.get<ApiResponse<{ user: User }>>(`/api/users/${id}`)
  },

  // 创建用户
  createUser: (userData: CreateUserRequest) => {
    return apiClient.post<ApiResponse<{ user: User }>>('/api/users', userData)
  },

  // 更新用户
  updateUser: (id: string, userData: UpdateUserRequest) => {
    return apiClient.put<ApiResponse<{ user: User }>>(`/api/users/${id}`, userData)
  },

  // 删除用户
  deleteUser: (id: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/users/${id}`)
  },

  // 激活/停用用户
  toggleUserStatus: (id: string, isActive: boolean) => {
    return apiClient.patch<ApiResponse<{ user: User }>>(`/api/users/${id}/status`, { isActive })
  },

  // 重置用户密码
  resetUserPassword: (id: string, newPassword: string) => {
    return apiClient.patch<ApiResponse<{}>>(`/api/users/${id}/reset-password`, { password: newPassword })
  },

  // 获取用户统计
  getUserStats: () => {
    return apiClient.get<ApiResponse<{
      totalUsers: number
      activeUsers: number
      newUsersThisMonth: number
      roleDistribution: Array<{
        role: string
        count: number
        percentage: number
      }>
      activityStats: Array<{
        date: string
        activeUsers: number
        newUsers: number
      }>
    }>>('/api/users/stats')
  },

  // 获取用户活动日志
  getUserActivity: (id: string, params?: {
    page?: number
    pageSize?: number
    dateRange?: [string, string]
    action?: string
  }) => {
    return apiClient.get<ApiResponse<{
      activities: Array<{
        id: string
        action: string
        description: string
        timestamp: string
        ipAddress?: string
        userAgent?: string
        metadata?: any
      }>
      pagination: {
        current: number
        pageSize: number
        total: number
      }
    }>>(`/api/users/${id}/activity`, { params })
  },

  // 批量操作用户
  batchUpdateUsers: (userIds: string[], updates: UpdateUserRequest) => {
    return apiClient.patch<ApiResponse<{ updatedCount: number }>>('/api/users/batch', {
      userIds,
      updates
    })
  },

  // 批量删除用户
  batchDeleteUsers: (userIds: string[]) => {
    return apiClient.delete<ApiResponse<{ deletedCount: number }>>('/api/users/batch', {
      data: { userIds }
    })
  },

  // 导出用户数据
  exportUsers: (params: UserQueryParams & { format: 'excel' | 'csv' }) => {
    return apiClient.get('/api/users/export', {
      params,
      responseType: 'blob'
    })
  },

  // 导入用户数据
  importUsers: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    
    return apiClient.post<ApiResponse<{
      imported: number
      failed: number
      errors: Array<{
        row: number
        error: string
      }>
    }>>('/api/users/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 发送邀请邮件
  sendInvitation: (email: string, role: User['role'], message?: string) => {
    return apiClient.post<ApiResponse<{ invitationId: string }>>('/api/users/invite', {
      email,
      role,
      message
    })
  },

  // 获取邀请列表
  getInvitations: (params?: {
    page?: number
    pageSize?: number
    status?: 'pending' | 'accepted' | 'expired'
  }) => {
    return apiClient.get<ApiResponse<{
      invitations: Array<{
        id: string
        email: string
        role: string
        status: string
        sentAt: string
        expiresAt: string
        acceptedAt?: string
      }>
      pagination: {
        current: number
        pageSize: number
        total: number
      }
    }>>('/api/users/invitations', { params })
  },

  // 取消邀请
  cancelInvitation: (invitationId: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/users/invitations/${invitationId}`)
  },

  // 重新发送邀请
  resendInvitation: (invitationId: string) => {
    return apiClient.post<ApiResponse<{}>>(`/api/users/invitations/${invitationId}/resend`)
  },

  // 接受邀请
  acceptInvitation: (token: string, userData: {
    name: string
    password: string
  }) => {
    return apiClient.post<ApiResponse<{ user: User; token: string }>>('/api/users/accept-invitation', {
      token,
      ...userData
    })
  },

  // 获取用户权限
  getUserPermissions: (id: string) => {
    return apiClient.get<ApiResponse<{
      permissions: Array<{
        resource: string
        actions: string[]
      }>
    }>>(`/api/users/${id}/permissions`)
  },

  // 更新用户权限
  updateUserPermissions: (id: string, permissions: Array<{
    resource: string
    actions: string[]
  }>) => {
    return apiClient.put<ApiResponse<{}>>(`/api/users/${id}/permissions`, { permissions })
  },

  // 获取用户偏好设置
  getUserPreferences: (id: string) => {
    return apiClient.get<ApiResponse<{
      preferences: {
        language: string
        timezone: string
        dateFormat: string
        notifications: {
          email: boolean
          push: boolean
          sms: boolean
        }
        dashboard: {
          layout: string
          widgets: string[]
        }
      }
    }>>(`/api/users/${id}/preferences`)
  },

  // 更新用户偏好设置
  updateUserPreferences: (id: string, preferences: any) => {
    return apiClient.put<ApiResponse<{}>>(`/api/users/${id}/preferences`, { preferences })
  },
}

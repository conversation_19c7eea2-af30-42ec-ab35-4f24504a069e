import { aiClient, apiClient, ApiResponse, uploadConfig } from './index'
import { AIAnalysis, AIAnalysisForm, AnalysisStatus } from '../../types'

// 分析查询参数
export interface AnalysisQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: AnalysisStatus
  batchId?: string
  dateRange?: [string, string]
  sortBy?: 'createdAt' | 'status' | 'confidence'
  sortOrder?: 'asc' | 'desc'
}

// 分析列表响应
export interface AnalysisListResponse {
  analyses: AIAnalysis[]
  pagination: {
    current: number
    pageSize: number
    total: number
  }
}

// 批量分析请求
export interface BatchAnalysisRequest {
  batchId: string
  images: string[] // Base64编码的图像或图像URL
  magnification: number
  stainingMethod: string
}

// 分析状态响应
export interface AnalysisStatusResponse {
  taskId: string
  status: AnalysisStatus
  progress: number
  message?: string
  result?: AIAnalysis
  estimatedTimeRemaining?: number
}

// AI分析API类
export const analysisAPI = {
  // 获取分析列表
  getAnalyses: (params?: AnalysisQueryParams) => {
    return apiClient.get<ApiResponse<AnalysisListResponse>>('/api/analysis', { params })
  },

  // 获取分析详情
  getAnalysisById: (id: string) => {
    return apiClient.get<ApiResponse<{ analysis: AIAnalysis }>>(`/api/analysis/${id}`)
  },

  // 提交单个图像分析
  submitAnalysis: (analysisData: AIAnalysisForm, onProgress?: (progress: number) => void) => {
    const formData = new FormData()
    
    // 添加图像文件
    analysisData.images.forEach((image, index) => {
      formData.append('images', image, `image_${index}.jpg`)
    })
    
    // 添加其他参数
    formData.append('magnification', analysisData.magnification.toString())
    formData.append('stainingMethod', analysisData.stainingMethod)
    
    return aiClient.post<ApiResponse<{ analysis: AIAnalysis }>>('/analyze', formData, {
      ...uploadConfig,
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
  },

  // 提交批量分析（异步）
  submitBatchAnalysis: (analysisData: BatchAnalysisRequest) => {
    return aiClient.post<ApiResponse<{ taskId: string }>>('/analyze/batch', analysisData)
  },

  // 获取分析任务状态
  getAnalysisStatus: (taskId: string) => {
    return aiClient.get<ApiResponse<AnalysisStatusResponse>>(`/analyze/status/${taskId}`)
  },

  // 取消分析任务
  cancelAnalysis: (taskId: string) => {
    return aiClient.delete<ApiResponse<{}>>(`/analyze/cancel/${taskId}`)
  },

  // 重新分析
  reanalyzeImage: (analysisId: string, options?: {
    magnification?: number
    stainingMethod?: string
    modelVersion?: string
  }) => {
    return aiClient.post<ApiResponse<{ analysis: AIAnalysis }>>(`/analyze/retry/${analysisId}`, options)
  },

  // 删除分析结果
  deleteAnalysis: (id: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/analysis/${id}`)
  },

  // 获取分析统计
  getAnalysisStats: (filters?: {
    dateRange?: [string, string]
    batchId?: string
    status?: AnalysisStatus
  }) => {
    return apiClient.get<ApiResponse<{
      totalAnalyses: number
      completedAnalyses: number
      failedAnalyses: number
      avgConfidence: number
      avgProcessingTime: number
      statusDistribution: Array<{
        status: AnalysisStatus
        count: number
        percentage: number
      }>
      confidenceDistribution: Array<{
        range: string
        count: number
        percentage: number
      }>
    }>>('/api/analysis/stats', { params: filters })
  },

  // 获取模型信息
  getModelInfo: () => {
    return aiClient.get<ApiResponse<{
      models: Array<{
        name: string
        version: string
        description: string
        accuracy: number
        supportedFormats: string[]
        isDefault: boolean
      }>
    }>>('/models')
  },

  // 获取分析历史趋势
  getAnalysisTrend: (params: {
    dateRange?: [string, string]
    interval?: 'day' | 'week' | 'month'
    metric?: 'count' | 'confidence' | 'processing_time'
  }) => {
    return apiClient.get<ApiResponse<{
      trend: Array<{
        date: string
        value: number
        count: number
      }>
    }>>('/api/analysis/trend', { params })
  },

  // 批量下载分析结果
  exportAnalyses: (params: AnalysisQueryParams & { format: 'excel' | 'csv' | 'pdf' }) => {
    return apiClient.get('/api/analysis/export', {
      params,
      responseType: 'blob'
    })
  },

  // 获取分析报告
  getAnalysisReport: (id: string, format: 'pdf' | 'html') => {
    return apiClient.get(`/api/analysis/${id}/report`, {
      params: { format },
      responseType: format === 'pdf' ? 'blob' : 'json'
    })
  },

  // 分享分析结果
  shareAnalysis: (id: string, options: {
    recipients: string[]
    message?: string
    includeImages?: boolean
  }) => {
    return apiClient.post<ApiResponse<{ shareId: string }>>(`/api/analysis/${id}/share`, options)
  },

  // 获取共享的分析结果
  getSharedAnalysis: (shareId: string) => {
    return apiClient.get<ApiResponse<{ analysis: AIAnalysis }>>(`/api/analysis/shared/${shareId}`)
  },

  // 标记分析结果
  markAnalysis: (id: string, mark: {
    type: 'favorite' | 'important' | 'reviewed'
    value: boolean
    note?: string
  }) => {
    return apiClient.patch<ApiResponse<{ analysis: AIAnalysis }>>(`/api/analysis/${id}/mark`, mark)
  },

  // 获取AI服务健康状态
  getAIServiceHealth: () => {
    return aiClient.get<ApiResponse<{
      status: 'healthy' | 'degraded' | 'unhealthy'
      services: Record<string, string>
      modelsLoaded: string[]
      queueSize: number
      avgProcessingTime: number
    }>>('/health')
  },

  // 预处理图像
  preprocessImage: (image: File, options?: {
    resize?: { width: number; height: number }
    enhance?: boolean
    normalize?: boolean
  }) => {
    const formData = new FormData()
    formData.append('image', image)
    if (options) {
      formData.append('options', JSON.stringify(options))
    }
    
    return aiClient.post<ApiResponse<{ processedImage: string }>>('/preprocess', formData, uploadConfig)
  },

  // 获取图像质量评估
  assessImageQuality: (image: File) => {
    const formData = new FormData()
    formData.append('image', image)
    
    return aiClient.post<ApiResponse<{
      quality: {
        score: number
        issues: string[]
        recommendations: string[]
      }
    }>>('/assess-quality', formData, uploadConfig)
  },
}

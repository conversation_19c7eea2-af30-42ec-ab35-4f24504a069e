import { apiClient, ApiResponse } from './index'
import { User } from '../../types'

// 认证相关的API接口
export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  user: User
  token: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirmPassword?: string
}

export interface RegisterResponse {
  user: User
  token: string
}

export interface ResetPasswordRequest {
  email: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 认证API类
export const authAPI = {
  // 用户登录
  login: (credentials: LoginRequest) => {
    return apiClient.post<ApiResponse<LoginResponse>>('/api/auth/login', credentials)
  },

  // 用户注册
  register: (userData: RegisterRequest) => {
    return apiClient.post<ApiResponse<RegisterResponse>>('/api/auth/register', userData)
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    return apiClient.get<ApiResponse<{ user: User }>>('/api/auth/me')
  },

  // 刷新token
  refreshToken: () => {
    return apiClient.post<ApiResponse<{ token: string }>>('/api/auth/refresh')
  },

  // 用户登出
  logout: () => {
    return apiClient.post<ApiResponse<{}>>('/api/auth/logout')
  },

  // 发送重置密码邮件
  requestPasswordReset: (data: ResetPasswordRequest) => {
    return apiClient.post<ApiResponse<{}>>('/api/auth/forgot-password', data)
  },

  // 重置密码
  resetPassword: (token: string, newPassword: string) => {
    return apiClient.post<ApiResponse<{}>>('/api/auth/reset-password', {
      token,
      password: newPassword,
    })
  },

  // 修改密码
  changePassword: (data: ChangePasswordRequest) => {
    return apiClient.put<ApiResponse<{}>>('/api/auth/change-password', data)
  },

  // 更新个人资料
  updateProfile: (userData: Partial<User>) => {
    return apiClient.put<ApiResponse<{ user: User }>>('/api/auth/profile', userData)
  },

  // 验证邮箱
  verifyEmail: (token: string) => {
    return apiClient.post<ApiResponse<{}>>('/api/auth/verify-email', { token })
  },

  // 重新发送验证邮件
  resendVerificationEmail: () => {
    return apiClient.post<ApiResponse<{}>>('/api/auth/resend-verification')
  },

  // 检查邮箱是否已存在
  checkEmailExists: (email: string) => {
    return apiClient.get<ApiResponse<{ exists: boolean }>>(`/api/auth/check-email?email=${encodeURIComponent(email)}`)
  },

  // 验证token有效性
  validateToken: (token: string) => {
    return apiClient.post<ApiResponse<{ valid: boolean; user?: User }>>('/api/auth/validate-token', { token })
  },
}

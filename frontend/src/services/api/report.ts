import { apiClient, ApiResponse } from './index'

// 报告类型定义
export interface QualityReport {
  id: string
  title: string
  type: 'quality_summary' | 'batch_analysis' | 'trend_analysis' | 'custom'
  dateRange: [string, string]
  filters: Record<string, any>
  data: any
  createdAt: string
  createdBy: {
    id: string
    name: string
  }
}

// 报告查询参数
export interface ReportQueryParams {
  page?: number
  pageSize?: number
  type?: QualityReport['type']
  dateRange?: [string, string]
  createdBy?: string
  sortBy?: 'createdAt' | 'title' | 'type'
  sortOrder?: 'asc' | 'desc'
}

// 报告生成请求
export interface GenerateReportRequest {
  title: string
  type: QualityReport['type']
  dateRange: [string, string]
  filters?: {
    recipeIds?: string[]
    batchIds?: string[]
    status?: string[]
    includeAnalysis?: boolean
    includeImages?: boolean
  }
  template?: string
  format?: 'pdf' | 'excel' | 'html'
}

// 报告API类
export const reportAPI = {
  // 获取报告列表
  getReports: (params?: ReportQueryParams) => {
    return apiClient.get<ApiResponse<{
      reports: QualityReport[]
      pagination: {
        current: number
        pageSize: number
        total: number
      }
    }>>('/api/reports', { params })
  },

  // 获取报告详情
  getReportById: (id: string) => {
    return apiClient.get<ApiResponse<{ report: QualityReport }>>(`/api/reports/${id}`)
  },

  // 生成报告
  generateReport: (reportData: GenerateReportRequest) => {
    return apiClient.post<ApiResponse<{ report: QualityReport }>>('/api/reports/generate', reportData)
  },

  // 下载报告
  downloadReport: (id: string, format: 'pdf' | 'excel' | 'html') => {
    return apiClient.get(`/api/reports/${id}/download`, {
      params: { format },
      responseType: 'blob'
    })
  },

  // 删除报告
  deleteReport: (id: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/reports/${id}`)
  },

  // 获取质量总结报告
  getQualitySummary: (params: {
    dateRange: [string, string]
    recipeIds?: string[]
    includeComparison?: boolean
  }) => {
    return apiClient.get<ApiResponse<{
      summary: {
        totalBatches: number
        completedBatches: number
        avgQualityScore: number
        successRate: number
        topPerformingRecipes: Array<{
          id: string
          name: string
          batchCount: number
          avgQualityScore: number
        }>
        qualityTrend: Array<{
          date: string
          score: number
          batchCount: number
        }>
        issuesSummary: Array<{
          type: string
          count: number
          severity: 'low' | 'medium' | 'high'
        }>
      }
    }>>('/api/reports/quality-summary', { params })
  },

  // 获取批次分析报告
  getBatchAnalysisReport: (batchId: string) => {
    return apiClient.get<ApiResponse<{
      report: {
        batch: any
        recipe: any
        sensoryAssessment?: any
        aiAnalyses: any[]
        qualityMetrics: {
          overallScore: number
          textureScore: number
          acidityScore: number
          flavorScore: number
          microbiologyScore?: number
        }
        recommendations: string[]
        timeline: Array<{
          timestamp: string
          event: string
          description: string
        }>
      }
    }>>(`/api/reports/batch-analysis/${batchId}`)
  },

  // 获取趋势分析报告
  getTrendAnalysisReport: (params: {
    dateRange: [string, string]
    metric: 'quality' | 'production' | 'efficiency'
    groupBy: 'day' | 'week' | 'month'
    recipeIds?: string[]
  }) => {
    return apiClient.get<ApiResponse<{
      report: {
        trend: Array<{
          period: string
          value: number
          change: number
          changePercent: number
        }>
        insights: string[]
        recommendations: string[]
        forecast?: Array<{
          period: string
          predicted: number
          confidence: number
        }>
      }
    }>>('/api/reports/trend-analysis', { params })
  },

  // 获取对比分析报告
  getComparisonReport: (params: {
    type: 'recipe' | 'batch' | 'period'
    items: string[]
    metrics: string[]
  }) => {
    return apiClient.get<ApiResponse<{
      report: {
        comparison: Array<{
          item: any
          metrics: Record<string, number>
          rank: number
        }>
        insights: string[]
        recommendations: string[]
      }
    }>>('/api/reports/comparison', { params })
  },

  // 获取报告模板
  getReportTemplates: () => {
    return apiClient.get<ApiResponse<{
      templates: Array<{
        id: string
        name: string
        description: string
        type: QualityReport['type']
        fields: Array<{
          name: string
          type: string
          required: boolean
          options?: string[]
        }>
      }>
    }>>('/api/reports/templates')
  },

  // 创建自定义报告模板
  createReportTemplate: (template: {
    name: string
    description: string
    type: QualityReport['type']
    config: any
  }) => {
    return apiClient.post<ApiResponse<{ template: any }>>('/api/reports/templates', template)
  },

  // 分享报告
  shareReport: (id: string, options: {
    recipients: string[]
    message?: string
    expiresAt?: string
  }) => {
    return apiClient.post<ApiResponse<{ shareId: string }>>(`/api/reports/${id}/share`, options)
  },

  // 获取共享报告
  getSharedReport: (shareId: string) => {
    return apiClient.get<ApiResponse<{ report: QualityReport }>>(`/api/reports/shared/${shareId}`)
  },

  // 获取报告统计
  getReportStats: () => {
    return apiClient.get<ApiResponse<{
      totalReports: number
      recentReports: number
      popularTypes: Array<{
        type: string
        count: number
      }>
      avgGenerationTime: number
    }>>('/api/reports/stats')
  },

  // 导出多个报告
  exportReports: (reportIds: string[], format: 'zip' | 'pdf') => {
    return apiClient.post('/api/reports/export', 
      { reportIds, format },
      { responseType: 'blob' }
    )
  },

  // 调度报告生成
  scheduleReport: (schedule: {
    reportConfig: GenerateReportRequest
    frequency: 'daily' | 'weekly' | 'monthly'
    recipients: string[]
    nextRunAt: string
  }) => {
    return apiClient.post<ApiResponse<{ scheduleId: string }>>('/api/reports/schedule', schedule)
  },

  // 获取调度的报告
  getScheduledReports: () => {
    return apiClient.get<ApiResponse<{
      schedules: Array<{
        id: string
        reportConfig: GenerateReportRequest
        frequency: string
        recipients: string[]
        nextRunAt: string
        lastRunAt?: string
        isActive: boolean
      }>
    }>>('/api/reports/schedules')
  },

  // 取消调度报告
  cancelScheduledReport: (scheduleId: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/reports/schedules/${scheduleId}`)
  },
}

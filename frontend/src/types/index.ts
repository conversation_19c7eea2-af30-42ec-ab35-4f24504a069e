// =============================================================================
// Yoghurt AI QC - 前端类型定义
// =============================================================================

// 基础类型
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// 用户相关类型
export interface User extends BaseEntity {
  email: string;
  name: string;
  role: 'admin' | 'user' | 'viewer';
  isActive: boolean;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// 配方相关类型
export interface Ingredient {
  name: string;
  amount: number;
  unit: string;
}

export interface ProcessStep {
  step: number;
  description: string;
  duration?: number; // 分钟
  temperature?: number; // 摄氏度
}

export interface Recipe extends BaseEntity {
  userId: string;
  name: string;
  description?: string;
  ingredients: Ingredient[];
  process: ProcessStep[];
  fermentationTemperature?: number;
  fermentationDuration?: number; // 小时
  filtrationDuration?: number; // 小时
  version: number;
  isActive: boolean;
}

// 批次相关类型
export type BatchStatus = 
  | 'planning' 
  | 'in_progress' 
  | 'fermenting' 
  | 'filtering' 
  | 'completed' 
  | 'failed';

export interface Batch extends BaseEntity {
  batchNumber: string;
  recipeId: string;
  userId: string;
  status: BatchStatus;
  productionDate: string;
  quantity?: number; // L 或 kg
  actualFermentationDuration?: number;
  actualTemperature?: number;
  notes?: string;
  recipe?: Recipe; // 关联的配方信息
}

// 感官评估类型
export interface SensoryAssessment extends BaseEntity {
  batchId: string;
  assessorId: string;
  textureScore: number; // 1-5
  acidityScore: number; // 1-5
  flavorTags: string[];
  flavorNotes?: string;
  overallScore: number; // 1-5
  notes?: string;
  assessor?: User;
}

// AI 分析类型
export type AnalysisStatus =
  | 'idle'
  | 'uploading'
  | 'processing'
  | 'completed'
  | 'failed'

export interface AIAnalysisResult {
  qualityScore: number;
  bacterialCount?: number;
  contaminationDetected: boolean;
  contaminationType?: string;
  morphologyAnalysis: {
    bacterialMorphology: string;
    balanceRatio: string;
    activityLevel: string;
  };
  confidenceScore: number;
  summaryAndRecommendation: string;
}

export interface AIAnalysis extends BaseEntity {
  batchId: string;
  imageUrls: string[];
  magnification: number;
  stainingMethod: string;
  analysisResult: AIAnalysisResult;
  qualityScore?: number;
  confidenceScore: number;
  contaminationDetected: boolean;
  contaminationType?: string;
  bacterialCount?: number;
  processingTimeMs: number;
  modelVersion?: string;
  apiProvider?: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 表单类型
export interface CreateRecipeForm {
  name: string;
  description?: string;
  ingredients: Ingredient[];
  process: ProcessStep[];
  fermentationTemperature?: number;
  fermentationDuration?: number;
  filtrationDuration?: number;
}

export interface CreateBatchForm {
  recipeId: string;
  quantity?: number;
  notes?: string;
}

export interface SensoryAssessmentForm {
  textureScore: number;
  acidityScore: number;
  flavorTags: string[];
  flavorNotes?: string;
  overallScore: number;
  notes?: string;
}

export interface AIAnalysisForm {
  images: File[];
  magnification: number;
  stainingMethod: string;
}

// 图表数据类型
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface QualityTrendData {
  date: string;
  qualityScore: number;
  aiScore?: number;
  batchCount: number;
}

export interface RecipeUsageStats {
  recipeId: string;
  recipeName: string;
  usageCount: number;
  avgQualityScore: number;
  lastUsed: string;
}

// 通知类型
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  duration?: number;
  timestamp: string;
}

// 应用状态类型
export interface AppState {
  loading: boolean;
  error: string | null;
  notifications: Notification[];
}

// 路由类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  title: string;
  icon?: React.ReactNode;
  requireAuth?: boolean;
  roles?: string[];
}

// 文件上传类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error';
  url?: string;
  response?: any;
  error?: any;
}

// 搜索和筛选类型
export interface SearchFilters {
  keyword?: string;
  dateRange?: [string, string];
  status?: string[];
  userId?: string;
  recipeId?: string;
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

// 导出类型
export interface ExportConfig {
  format: 'excel' | 'pdf' | 'csv';
  fields: string[];
  filters?: SearchFilters;
  dateRange?: [string, string];
}

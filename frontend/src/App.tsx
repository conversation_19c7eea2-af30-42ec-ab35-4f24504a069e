import React, { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { App as AntApp } from 'antd'

import { checkAuthStatus } from './store/slices/authSlice'
import AppRouter from './router'

const App: React.FC = () => {
  const dispatch = useDispatch()

  useEffect(() => {
    // 检查认证状态
    const token = localStorage.getItem('token')
    if (token) {
      dispatch(checkAuthStatus() as any)
    }
  }, [dispatch])

  return (
    <AntApp>
      <AppRouter />
    </AntApp>
  )
}

export default App

import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ErrorBoundary } from 'react-error-boundary'

// 组件导入
import Layout from '../components/Layout'
import ProtectedRoute from '../components/ProtectedRoute'
import ErrorFallback from '../components/ErrorFallback'
import LoadingSpinner from '../components/LoadingSpinner'

// 页面组件懒加载
const Dashboard = React.lazy(() => import('../pages/Dashboard'))
const Login = React.lazy(() => import('../pages/Login'))
const Recipes = React.lazy(() => import('../pages/Recipes'))
const RecipeDetail = React.lazy(() => import('../pages/RecipeDetail'))
const Batches = React.lazy(() => import('../pages/Batches'))
const BatchDetail = React.lazy(() => import('../pages/BatchDetail'))
const Analysis = React.lazy(() => import('../pages/Analysis'))
const Reports = React.lazy(() => import('../pages/Reports'))
const Settings = React.lazy(() => import('../pages/Settings'))
const Profile = React.lazy(() => import('../pages/Profile'))
const NotFound = React.lazy(() => import('../pages/NotFound'))

// 路由配置
const AppRouter: React.FC = () => {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingSpinner tip="加载页面中..." />}>
        <Routes>
          {/* 登录页面 */}
          <Route path="/login" element={<Login />} />
          
          {/* 受保护的路由 */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    {/* 仪表板 */}
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/dashboard" element={<Navigate to="/" replace />} />
                    
                    {/* 配方管理 */}
                    <Route path="/recipes" element={<Recipes />} />
                    <Route path="/recipes/:id" element={<RecipeDetail />} />
                    <Route path="/recipes/:id/edit" element={<RecipeDetail />} />
                    <Route path="/recipes/new" element={<RecipeDetail />} />
                    
                    {/* 批次管理 */}
                    <Route path="/batches" element={<Batches />} />
                    <Route path="/batches/:id" element={<BatchDetail />} />
                    <Route path="/batches/:id/edit" element={<BatchDetail />} />
                    <Route path="/batches/new" element={<BatchDetail />} />
                    
                    {/* AI分析 */}
                    <Route path="/analysis" element={<Analysis />} />
                    <Route path="/analysis/:id" element={<Analysis />} />
                    
                    {/* 报告中心 */}
                    <Route path="/reports" element={<Reports />} />
                    <Route path="/reports/:id" element={<Reports />} />
                    
                    {/* 系统设置 */}
                    <Route 
                      path="/settings" 
                      element={
                        <ProtectedRoute requiredRoles={['admin', 'manager']}>
                          <Settings />
                        </ProtectedRoute>
                      } 
                    />
                    
                    {/* 个人资料 */}
                    <Route path="/profile" element={<Profile />} />
                    
                    {/* 404页面 */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  )
}

export default AppRouter

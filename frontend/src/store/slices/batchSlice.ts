import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { <PERSON><PERSON>, BatchStatus, CreateBatchForm, SensoryAssessmentForm } from '../../types'
import { batchAPI } from '../../services/api'

interface BatchState {
  batches: Batch[]
  currentBatch: Batch | null
  loading: boolean
  error: string | null
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  filters: {
    search?: string
    status?: BatchStatus
    recipeId?: string
    dateRange?: [string, string]
  }
}

// 初始状态
const initialState: BatchState = {
  batches: [],
  currentBatch: null,
  loading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  filters: {},
}

// 异步actions
export const fetchBatches = createAsyncThunk(
  'batch/fetchBatches',
  async (params: { 
    page?: number
    pageSize?: number
    search?: string
    status?: BatchStatus
    recipeId?: string
    dateRange?: [string, string]
  }, { rejectWithValue }) => {
    try {
      const response = await batchAPI.getBatches(params)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次列表失败')
    }
  }
)

export const fetchBatchById = createAsyncThunk(
  'batch/fetchBatchById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await batchAPI.getBatchById(id)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次详情失败')
    }
  }
)

export const createBatch = createAsyncThunk(
  'batch/createBatch',
  async (batchData: CreateBatchForm, { rejectWithValue }) => {
    try {
      const response = await batchAPI.createBatch(batchData)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建批次失败')
    }
  }
)

export const updateBatch = createAsyncThunk(
  'batch/updateBatch',
  async ({ id, data }: { id: string; data: Partial<CreateBatchForm> }, { rejectWithValue }) => {
    try {
      const response = await batchAPI.updateBatch(id, data)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新批次失败')
    }
  }
)

export const updateBatchStatus = createAsyncThunk(
  'batch/updateBatchStatus',
  async ({ id, status }: { id: string; status: BatchStatus }, { rejectWithValue }) => {
    try {
      const response = await batchAPI.updateBatchStatus(id, status)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新批次状态失败')
    }
  }
)

export const submitSensoryAssessment = createAsyncThunk(
  'batch/submitSensoryAssessment',
  async ({ batchId, assessment }: { batchId: string; assessment: SensoryAssessmentForm }, { rejectWithValue }) => {
    try {
      const response = await batchAPI.submitSensoryAssessment(batchId, assessment)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '提交感官评估失败')
    }
  }
)

export const deleteBatch = createAsyncThunk(
  'batch/deleteBatch',
  async (id: string, { rejectWithValue }) => {
    try {
      await batchAPI.deleteBatch(id)
      return id
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除批次失败')
    }
  }
)

// 创建slice
const batchSlice = createSlice({
  name: 'batch',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCurrentBatch: (state, action: PayloadAction<Batch | null>) => {
      state.currentBatch = action.payload
    },
    setPagination: (state, action: PayloadAction<Partial<BatchState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
    setFilters: (state, action: PayloadAction<BatchState['filters']>) => {
      state.filters = action.payload
    },
    clearFilters: (state) => {
      state.filters = {}
    },
  },
  extraReducers: (builder) => {
    // 获取批次列表
    builder
      .addCase(fetchBatches.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchBatches.fulfilled, (state, action) => {
        state.loading = false
        state.batches = action.payload.batches
        state.pagination = {
          current: action.payload.pagination.current,
          pageSize: action.payload.pagination.pageSize,
          total: action.payload.pagination.total,
        }
      })
      .addCase(fetchBatches.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 获取批次详情
    builder
      .addCase(fetchBatchById.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchBatchById.fulfilled, (state, action) => {
        state.loading = false
        state.currentBatch = action.payload.batch
      })
      .addCase(fetchBatchById.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 创建批次
    builder
      .addCase(createBatch.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createBatch.fulfilled, (state, action) => {
        state.loading = false
        state.batches.unshift(action.payload.batch)
        state.pagination.total += 1
      })
      .addCase(createBatch.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 更新批次
    builder
      .addCase(updateBatch.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateBatch.fulfilled, (state, action) => {
        state.loading = false
        const index = state.batches.findIndex(batch => batch.id === action.payload.batch.id)
        if (index !== -1) {
          state.batches[index] = action.payload.batch
        }
        if (state.currentBatch?.id === action.payload.batch.id) {
          state.currentBatch = action.payload.batch
        }
      })
      .addCase(updateBatch.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 更新批次状态
    builder
      .addCase(updateBatchStatus.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateBatchStatus.fulfilled, (state, action) => {
        state.loading = false
        const index = state.batches.findIndex(batch => batch.id === action.payload.batch.id)
        if (index !== -1) {
          state.batches[index] = action.payload.batch
        }
        if (state.currentBatch?.id === action.payload.batch.id) {
          state.currentBatch = action.payload.batch
        }
      })
      .addCase(updateBatchStatus.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 提交感官评估
    builder
      .addCase(submitSensoryAssessment.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(submitSensoryAssessment.fulfilled, (state, action) => {
        state.loading = false
        const index = state.batches.findIndex(batch => batch.id === action.payload.batch.id)
        if (index !== -1) {
          state.batches[index] = action.payload.batch
        }
        if (state.currentBatch?.id === action.payload.batch.id) {
          state.currentBatch = action.payload.batch
        }
      })
      .addCase(submitSensoryAssessment.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 删除批次
    builder
      .addCase(deleteBatch.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteBatch.fulfilled, (state, action) => {
        state.loading = false
        state.batches = state.batches.filter(batch => batch.id !== action.payload)
        state.pagination.total -= 1
        if (state.currentBatch?.id === action.payload) {
          state.currentBatch = null
        }
      })
      .addCase(deleteBatch.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const {
  clearError,
  setCurrentBatch,
  setPagination,
  setFilters,
  clearFilters,
} = batchSlice.actions

export default batchSlice.reducer

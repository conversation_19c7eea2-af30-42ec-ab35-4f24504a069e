import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { AIAnalysis, AIAnalysisForm, AnalysisStatus } from '../../types'
import { analysisAPI } from '../../services/api'

interface AnalysisState {
  analyses: AIAnalysis[]
  currentAnalysis: AIAnalysis | null
  loading: boolean
  error: string | null
  uploadProgress: number
  analysisProgress: {
    taskId?: string
    status: AnalysisStatus
    progress: number
    message?: string
  }
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  filters: {
    search?: string
    status?: AnalysisStatus
    batchId?: string
    dateRange?: [string, string]
  }
}

// 初始状态
const initialState: AnalysisState = {
  analyses: [],
  currentAnalysis: null,
  loading: false,
  error: null,
  uploadProgress: 0,
  analysisProgress: {
    status: 'idle',
    progress: 0,
  },
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  filters: {},
}

// 异步actions
export const fetchAnalyses = createAsyncThunk(
  'analysis/fetchAnalyses',
  async (params: { 
    page?: number
    pageSize?: number
    search?: string
    status?: AnalysisStatus
    batchId?: string
    dateRange?: [string, string]
  }, { rejectWithValue }) => {
    try {
      const response = await analysisAPI.getAnalyses(params)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取分析列表失败')
    }
  }
)

export const fetchAnalysisById = createAsyncThunk(
  'analysis/fetchAnalysisById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await analysisAPI.getAnalysisById(id)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取分析详情失败')
    }
  }
)

export const submitAnalysis = createAsyncThunk(
  'analysis/submitAnalysis',
  async (analysisData: AIAnalysisForm, { rejectWithValue, dispatch }) => {
    try {
      // 设置上传进度
      dispatch(setUploadProgress(0))
      
      const response = await analysisAPI.submitAnalysis(analysisData, (progress) => {
        dispatch(setUploadProgress(progress))
      })
      
      // 重置上传进度
      dispatch(setUploadProgress(0))
      
      return response.data
    } catch (error: any) {
      dispatch(setUploadProgress(0))
      return rejectWithValue(error.response?.data?.message || '提交分析失败')
    }
  }
)

export const submitBatchAnalysis = createAsyncThunk(
  'analysis/submitBatchAnalysis',
  async (analysisData: { batchId: string; images: string[]; magnification: number; stainingMethod: string }, { rejectWithValue, dispatch }) => {
    try {
      const response = await analysisAPI.submitBatchAnalysis(analysisData)
      
      // 开始轮询任务状态
      if (response.data?.taskId) {
        dispatch(setAnalysisProgress({
          taskId: response.data.taskId,
          status: 'processing',
          progress: 0,
          message: '分析任务已提交，正在处理中...'
        }))

        // 启动状态轮询
        dispatch(pollAnalysisStatus(response.data.taskId))
      }
      
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '提交批量分析失败')
    }
  }
)

export const pollAnalysisStatus = createAsyncThunk(
  'analysis/pollAnalysisStatus',
  async (taskId: string, { rejectWithValue, dispatch }) => {
    try {
      const response = await analysisAPI.getAnalysisStatus(taskId)
      
      dispatch(setAnalysisProgress({
        taskId,
        status: response.data?.status || 'idle',
        progress: response.data?.progress || 0,
        message: response.data?.message
      }))

      // 如果任务还在进行中，继续轮询
      if (response.data?.status === 'processing') {
        setTimeout(() => {
          dispatch(pollAnalysisStatus(taskId))
        }, 2000) // 每2秒轮询一次
      }
      
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取分析状态失败')
    }
  }
)

export const deleteAnalysis = createAsyncThunk(
  'analysis/deleteAnalysis',
  async (id: string, { rejectWithValue }) => {
    try {
      await analysisAPI.deleteAnalysis(id)
      return id
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除分析失败')
    }
  }
)

// 创建slice
const analysisSlice = createSlice({
  name: 'analysis',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCurrentAnalysis: (state, action: PayloadAction<AIAnalysis | null>) => {
      state.currentAnalysis = action.payload
    },
    setUploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload
    },
    setAnalysisProgress: (state, action: PayloadAction<AnalysisState['analysisProgress']>) => {
      state.analysisProgress = action.payload
    },
    resetAnalysisProgress: (state) => {
      state.analysisProgress = {
        status: 'idle',
        progress: 0,
      }
    },
    setPagination: (state, action: PayloadAction<Partial<AnalysisState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
    setFilters: (state, action: PayloadAction<AnalysisState['filters']>) => {
      state.filters = action.payload
    },
    clearFilters: (state) => {
      state.filters = {}
    },
  },
  extraReducers: (builder) => {
    // 获取分析列表
    builder
      .addCase(fetchAnalyses.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAnalyses.fulfilled, (state, action) => {
        state.loading = false
        state.analyses = action.payload?.analyses || []
        state.pagination = {
          current: action.payload?.pagination?.current || 1,
          pageSize: action.payload?.pagination?.pageSize || 10,
          total: action.payload?.pagination?.total || 0,
        }
      })
      .addCase(fetchAnalyses.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 获取分析详情
    builder
      .addCase(fetchAnalysisById.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAnalysisById.fulfilled, (state, action) => {
        state.loading = false
        state.currentAnalysis = action.payload?.analysis || null
      })
      .addCase(fetchAnalysisById.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 提交分析
    builder
      .addCase(submitAnalysis.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(submitAnalysis.fulfilled, (state, action) => {
        state.loading = false
        if (action.payload?.analysis) {
          state.analyses.unshift(action.payload.analysis)
          state.pagination.total += 1
        }
      })
      .addCase(submitAnalysis.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 提交批量分析
    builder
      .addCase(submitBatchAnalysis.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(submitBatchAnalysis.fulfilled, (state) => {
        state.loading = false
      })
      .addCase(submitBatchAnalysis.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
        state.analysisProgress = {
          status: 'failed',
          progress: 0,
          message: action.payload as string
        }
      })

    // 轮询分析状态
    builder
      .addCase(pollAnalysisStatus.fulfilled, (state, action) => {
        if (action.payload?.status === 'completed' && action.payload?.result) {
          // 分析完成，添加到列表中
          state.analyses.unshift(action.payload.result)
          state.pagination.total += 1
        }
      })

    // 删除分析
    builder
      .addCase(deleteAnalysis.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteAnalysis.fulfilled, (state, action) => {
        state.loading = false
        state.analyses = state.analyses.filter(analysis => analysis.id !== action.payload)
        state.pagination.total -= 1
        if (state.currentAnalysis?.id === action.payload) {
          state.currentAnalysis = null
        }
      })
      .addCase(deleteAnalysis.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const {
  clearError,
  setCurrentAnalysis,
  setUploadProgress,
  setAnalysisProgress,
  resetAnalysisProgress,
  setPagination,
  setFilters,
  clearFilters,
} = analysisSlice.actions

export default analysisSlice.reducer

import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { AppState, Notification } from '../../types'

// 初始状态
const initialState: AppState = {
  loading: false,
  error: null,
  notifications: [],
}

// 创建slice
const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    clearError: (state) => {
      state.error = null
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
      }
      state.notifications.unshift(notification)
      
      // 限制通知数量，最多保留50条
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50)
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        notification => notification.id !== action.payload
      )
    },
    clearNotifications: (state) => {
      state.notifications = []
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload)
      if (notification) {
        // 这里可以添加已读标记逻辑
      }
    },
  },
})

export const {
  setLoading,
  setError,
  clearError,
  addNotification,
  removeNotification,
  clearNotifications,
  markNotificationAsRead,
} = appSlice.actions

export default appSlice.reducer

// 便捷的通知创建函数
export const createNotification = (
  type: Notification['type'],
  title: string,
  message: string,
  duration?: number
) => addNotification({ type, title, message, duration })

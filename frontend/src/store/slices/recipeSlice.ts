import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Recipe, CreateRecipeForm } from '../../types'
import { recipeAPI } from '../../services/api'

interface RecipeState {
  recipes: Recipe[]
  currentRecipe: Recipe | null
  loading: boolean
  error: string | null
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  filters: {
    search?: string
    isActive?: boolean
    userId?: string
  }
}

// 初始状态
const initialState: RecipeState = {
  recipes: [],
  currentRecipe: null,
  loading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  filters: {},
}

// 异步actions
export const fetchRecipes = createAsyncThunk(
  'recipe/fetchRecipes',
  async (params: { page?: number; pageSize?: number; search?: string; isActive?: boolean }, { rejectWithValue }) => {
    try {
      const response = await recipeAPI.getRecipes(params)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取配方列表失败')
    }
  }
)

export const fetchRecipeById = createAsyncThunk(
  'recipe/fetchRecipeById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await recipeAPI.getRecipeById(id)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取配方详情失败')
    }
  }
)

export const createRecipe = createAsyncThunk(
  'recipe/createRecipe',
  async (recipeData: CreateRecipeForm, { rejectWithValue }) => {
    try {
      const response = await recipeAPI.createRecipe(recipeData)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建配方失败')
    }
  }
)

export const updateRecipe = createAsyncThunk(
  'recipe/updateRecipe',
  async ({ id, data }: { id: string; data: Partial<CreateRecipeForm> }, { rejectWithValue }) => {
    try {
      const response = await recipeAPI.updateRecipe(id, data)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新配方失败')
    }
  }
)

export const deleteRecipe = createAsyncThunk(
  'recipe/deleteRecipe',
  async (id: string, { rejectWithValue }) => {
    try {
      await recipeAPI.deleteRecipe(id)
      return id
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除配方失败')
    }
  }
)

export const duplicateRecipe = createAsyncThunk(
  'recipe/duplicateRecipe',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await recipeAPI.duplicateRecipe(id)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '复制配方失败')
    }
  }
)

// 创建slice
const recipeSlice = createSlice({
  name: 'recipe',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCurrentRecipe: (state, action: PayloadAction<Recipe | null>) => {
      state.currentRecipe = action.payload
    },
    setPagination: (state, action: PayloadAction<Partial<RecipeState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
    setFilters: (state, action: PayloadAction<RecipeState['filters']>) => {
      state.filters = action.payload
    },
    clearFilters: (state) => {
      state.filters = {}
    },
  },
  extraReducers: (builder) => {
    // 获取配方列表
    builder
      .addCase(fetchRecipes.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchRecipes.fulfilled, (state, action) => {
        state.loading = false
        state.recipes = action.payload?.data?.recipes || []
        state.pagination = {
          current: action.payload?.data?.pagination?.current || action.payload?.pagination?.current || 1,
          pageSize: action.payload?.data?.pagination?.pageSize || action.payload?.pagination?.pageSize || 10,
          total: action.payload?.data?.pagination?.total || action.payload?.pagination?.total || 0,
        }
      })
      .addCase(fetchRecipes.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 获取配方详情
    builder
      .addCase(fetchRecipeById.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchRecipeById.fulfilled, (state, action) => {
        state.loading = false
        state.currentRecipe = action.payload?.data?.recipe || null
      })
      .addCase(fetchRecipeById.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 创建配方
    builder
      .addCase(createRecipe.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createRecipe.fulfilled, (state, action) => {
        state.loading = false
        if (action.payload?.data?.recipe) {
          state.recipes.unshift(action.payload.data.recipe)
          state.pagination.total += 1
        }
      })
      .addCase(createRecipe.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 更新配方
    builder
      .addCase(updateRecipe.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateRecipe.fulfilled, (state, action) => {
        state.loading = false
        if (action.payload?.data?.recipe) {
          const index = state.recipes.findIndex(recipe => recipe.id === action.payload.data.recipe.id)
          if (index !== -1) {
            state.recipes[index] = action.payload.data.recipe
          }
          if (state.currentRecipe?.id === action.payload.data.recipe.id) {
            state.currentRecipe = action.payload.data.recipe
          }
        }
      })
      .addCase(updateRecipe.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 删除配方
    builder
      .addCase(deleteRecipe.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteRecipe.fulfilled, (state, action) => {
        state.loading = false
        state.recipes = state.recipes.filter(recipe => recipe.id !== action.payload)
        state.pagination.total -= 1
        if (state.currentRecipe?.id === action.payload) {
          state.currentRecipe = null
        }
      })
      .addCase(deleteRecipe.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })

    // 复制配方
    builder
      .addCase(duplicateRecipe.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(duplicateRecipe.fulfilled, (state, action) => {
        state.loading = false
        if (action.payload?.data?.recipe) {
          state.recipes.unshift(action.payload.data.recipe)
          state.pagination.total += 1
        }
      })
      .addCase(duplicateRecipe.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const {
  clearError,
  setCurrentRecipe,
  setPagination,
  setFilters,
  clearFilters,
} = recipeSlice.actions

export default recipeSlice.reducer

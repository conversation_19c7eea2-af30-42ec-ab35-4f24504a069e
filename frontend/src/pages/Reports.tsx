import React from 'react'
import { Card, Typography, Button, Space, Row, Col, Statistic } from 'antd'
import { FileTextOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const Reports: React.FC = () => {
  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>报告中心</Title>
          <Text type="secondary">生成和管理质量分析报告</Text>
        </div>
        <Space>
          <Button icon={<DownloadOutlined />}>导出报告</Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
          >
            生成报告
          </Button>
        </Space>
      </div>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="总报告数"
              value={45}
              prefix={<FileTextOutlined style={{ color: '#F05654' }} />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="本月报告"
              value={12}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="质量报告"
              value={28}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="趋势分析"
              value={17}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>
      
      <Card>
        <p>报告中心页面正在开发中...</p>
        <p>功能包括：</p>
        <ul>
          <li>质量趋势分析报告</li>
          <li>批次对比分析报告</li>
          <li>配方性能统计报告</li>
          <li>自定义报告生成</li>
          <li>报告导出和分享</li>
        </ul>
      </Card>
    </div>
  )
}

export default Reports

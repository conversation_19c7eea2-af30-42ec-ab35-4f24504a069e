import React from 'react'
import { Card, Typography, Tabs, Form, Input, Switch, Button, Space, Divider } from 'antd'
import { SettingOutlined, UserOutlined, BellOutlined, SecurityScanOutlined } from '@ant-design/icons'

const { Title, Text } = Typography
const { TabPane } = Tabs

const Settings: React.FC = () => {
  const [form] = Form.useForm()

  const handleSave = (values: any) => {
    console.log('保存设置:', values)
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>系统设置</Title>
        <Text type="secondary">管理系统配置和个人偏好</Text>
      </div>
      
      <Card>
        <Tabs defaultActiveKey="general" type="card">
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                常规设置
              </span>
            } 
            key="general"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              style={{ maxWidth: 600 }}
            >
              <Form.Item
                label="系统名称"
                name="systemName"
                initialValue="Yogurt AI QC"
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                label="默认语言"
                name="language"
                initialValue="zh-CN"
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                label="时区"
                name="timezone"
                initialValue="Asia/Shanghai"
              >
                <Input />
              </Form.Item>
              
              <Divider />
              
              <Form.Item
                label="启用AI分析"
                name="enableAI"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                label="自动备份"
                name="autoBackup"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    保存设置
                  </Button>
                  <Button>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <UserOutlined />
                用户管理
              </span>
            } 
            key="users"
          >
            <p>用户管理功能正在开发中...</p>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <BellOutlined />
                通知设置
              </span>
            } 
            key="notifications"
          >
            <p>通知设置功能正在开发中...</p>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <SecurityScanOutlined />
                安全设置
              </span>
            } 
            key="security"
          >
            <p>安全设置功能正在开发中...</p>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default Settings

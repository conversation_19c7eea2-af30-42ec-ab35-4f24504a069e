import React from 'react'
import { Card, Typography, Form, Input, Button, Space, Avatar, Upload, message } from 'antd'
import { UserOutlined, UploadOutlined } from '@ant-design/icons'
import { useSelector } from 'react-redux'
import { RootState } from '../store'

const { Title, Text } = Typography

const Profile: React.FC = () => {
  const [form] = Form.useForm()
  const { user } = useSelector((state: RootState) => state.auth)

  const handleSave = (values: any) => {
    console.log('保存个人资料:', values)
    message.success('个人资料更新成功')
  }

  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功')
    } else if (info.file.status === 'error') {
      message.error('头像上传失败')
    }
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>个人资料</Title>
        <Text type="secondary">管理您的个人信息和偏好设置</Text>
      </div>
      
      <Card>
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 32 }}>
          {/* 头像区域 */}
          <div style={{ textAlign: 'center' }}>
            <Avatar 
              size={120} 
              icon={<UserOutlined />}
              style={{ backgroundColor: '#F05654', marginBottom: 16 }}
            />
            <Upload
              name="avatar"
              showUploadList={false}
              onChange={handleAvatarUpload}
            >
              <Button icon={<UploadOutlined />} size="small">
                更换头像
              </Button>
            </Upload>
          </div>
          
          {/* 表单区域 */}
          <div style={{ flex: 1, maxWidth: 500 }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={{
                name: user?.name || '',
                email: user?.email || '',
                role: user?.role || '',
              }}
            >
              <Form.Item
                label="姓名"
                name="name"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                label="角色"
                name="role"
              >
                <Input disabled />
              </Form.Item>
              
              <Form.Item
                label="手机号"
                name="phone"
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                label="部门"
                name="department"
              >
                <Input />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    保存更改
                  </Button>
                  <Button>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        </div>
      </Card>
      
      {/* 密码修改 */}
      <Card title="修改密码" style={{ marginTop: 24 }}>
        <Form
          layout="vertical"
          style={{ maxWidth: 400 }}
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password />
          </Form.Item>
          
          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password />
          </Form.Item>
          
          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary">
              修改密码
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  )
}

export default Profile

/* =============================================================================
   Dashboard 页面样式
   ============================================================================= */

.dashboard-container {
  padding: 0;
  background: #fafafa;
  min-height: 100%;
}

/* 头部区域 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.dashboard-title {
  color: #262626 !important;
  margin: 0 !important;
  font-weight: 600;
  font-size: 28px;
}

.dashboard-subtitle {
  color: #A8ABB0;
  font-size: 16px;
  margin-top: 8px;
}

/* 统计卡片行 */
.stats-row {
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-card .ant-card-body {
  padding: 24px;
}

.stat-card .ant-statistic {
  text-align: center;
}

.stat-card .ant-statistic-title {
  color: #A8ABB0;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-card .ant-statistic-content {
  color: #262626;
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
}

.stat-card .ant-statistic-content-prefix {
  font-size: 24px;
  margin-right: 8px;
}

/* 趋势指示器 */
.trend-indicator {
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.trend-indicator.positive {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.trend-indicator.negative {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

/* 图表区域 */
.charts-row {
  margin-bottom: 32px;
}

.chart-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  height: 100%;
}

.chart-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.chart-card .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-card .ant-card-body {
  padding: 24px;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.table-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.table-card .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.table-card .ant-card-extra {
  padding: 0;
}

.table-card .ant-card-extra .ant-btn-link {
  color: #F05654;
  font-weight: 500;
}

.table-card .ant-card-extra .ant-btn-link:hover {
  color: #FF6B69;
}

.table-card .ant-card-body {
  padding: 0;
}

/* 表格样式 */
.table-card .ant-table {
  border-radius: 0;
}

.table-card .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
  padding: 16px 24px;
}

.table-card .ant-table-tbody > tr > td {
  padding: 16px 24px;
  border-bottom: 1px solid #f5f5f5;
}

.table-card .ant-table-tbody > tr:hover > td {
  background: rgba(240, 86, 84, 0.05);
}

.table-card .ant-table-tbody > tr:last-child > td {
  border-bottom: none;
}

/* 按钮样式 */
.dashboard-header .ant-btn-primary {
  background: #F05654;
  border-color: #F05654;
  border-radius: 8px;
  font-weight: 500;
  height: 40px;
  padding: 0 20px;
}

.dashboard-header .ant-btn-primary:hover {
  background: #FF6B69;
  border-color: #FF6B69;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 86, 84, 0.3);
}

.dashboard-header .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  height: 40px;
  padding: 0 20px;
  border-color: #d9d9d9;
}

.dashboard-header .ant-btn:hover {
  border-color: #F05654;
  color: #F05654;
}

/* 标签样式 */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 2px 8px;
  border: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .dashboard-header > div:last-child {
    display: flex;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 0;
  }
  
  .dashboard-header {
    margin-bottom: 24px;
    padding: 20px;
    border-radius: 8px;
  }
  
  .dashboard-title {
    font-size: 24px !important;
  }
  
  .dashboard-subtitle {
    font-size: 14px;
  }
  
  .stats-row {
    margin-bottom: 24px;
  }
  
  .charts-row {
    margin-bottom: 24px;
  }
  
  .stat-card .ant-card-body {
    padding: 20px;
  }
  
  .stat-card .ant-statistic-content {
    font-size: 28px;
  }
  
  .chart-card .ant-card-body {
    padding: 16px;
  }
  
  .table-card .ant-table-thead > tr > th,
  .table-card .ant-table-tbody > tr > td {
    padding: 12px 16px;
  }
}

@media (max-width: 576px) {
  .dashboard-header {
    padding: 16px;
  }
  
  .dashboard-header > div:last-child {
    flex-direction: column;
  }
  
  .dashboard-title {
    font-size: 20px !important;
  }
  
  .stat-card .ant-statistic-content {
    font-size: 24px;
  }
  
  .stat-card .ant-statistic-content-prefix {
    font-size: 20px;
  }
  
  .chart-card .ant-card-body {
    padding: 12px;
  }
  
  .table-card .ant-table-thead > tr > th,
  .table-card .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* 动画效果 */
.dashboard-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图表容器动画 */
.chart-card {
  animation: fadeIn 0.8s ease-out 0.5s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

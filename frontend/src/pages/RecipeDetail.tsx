import React from 'react'
import { <PERSON>, <PERSON>po<PERSON>, Button, Space } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useNavigate, useParams } from 'react-router-dom'

const { Title } = Typography

const RecipeDetail: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/recipes')}
          >
            返回配方列表
          </Button>
        </Space>
      </div>
      
      <Card>
        <Title level={2}>配方详情</Title>
        <p>配方ID: {id}</p>
        <p>此页面正在开发中...</p>
      </Card>
    </div>
  )
}

export default RecipeDetail

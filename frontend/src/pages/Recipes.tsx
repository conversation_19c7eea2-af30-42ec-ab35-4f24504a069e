import React, { useEffect, useState } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Dropdown, 
  Modal, 
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Select
} from 'antd'
import {
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExperimentOutlined,
  ExportOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { deleteRecipe, duplicateRecipe } from '../store/slices/recipeSlice'
import LoadingSpinner from '../components/LoadingSpinner'
import './Recipes.css'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select

// 模拟数据
const mockRecipes = [
  {
    id: '1',
    name: '经典原味酸奶',
    description: '传统发酵工艺，口感醇厚',
    version: 1,
    isActive: true,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-10',
    userId: 'user1',
    ingredients: [
      { name: '全脂牛奶', amount: 1000, unit: 'ml' },
      { name: '酸奶发酵剂', amount: 1, unit: '包' }
    ],
    fermentationTemperature: 42,
    fermentationDuration: 8,
    batchCount: 15,
    avgQualityScore: 92.5
  },
  {
    id: '2',
    name: '蓝莓风味酸奶',
    description: '添加新鲜蓝莓，酸甜可口',
    version: 2,
    isActive: true,
    createdAt: '2024-01-08',
    updatedAt: '2024-01-12',
    userId: 'user1',
    ingredients: [
      { name: '全脂牛奶', amount: 1000, unit: 'ml' },
      { name: '酸奶发酵剂', amount: 1, unit: '包' },
      { name: '蓝莓果酱', amount: 100, unit: 'g' }
    ],
    fermentationTemperature: 42,
    fermentationDuration: 8,
    batchCount: 8,
    avgQualityScore: 89.2
  },
  {
    id: '3',
    name: '希腊式酸奶',
    description: '浓稠质地，蛋白质含量高',
    version: 1,
    isActive: false,
    createdAt: '2024-01-05',
    updatedAt: '2024-01-05',
    userId: 'user2',
    ingredients: [
      { name: '全脂牛奶', amount: 1500, unit: 'ml' },
      { name: '希腊酸奶发酵剂', amount: 1, unit: '包' }
    ],
    fermentationTemperature: 45,
    fermentationDuration: 12,
    batchCount: 3,
    avgQualityScore: 94.8
  }
]

const Recipes: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  
  const navigate = useNavigate()
  const dispatch = useDispatch()
  // const { recipes, pagination } = useSelector((state: RootState) => state.recipe)

  useEffect(() => {
    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // 处理删除配方
  const handleDelete = (id: string, name: string) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除配方"${name}"吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deleteRecipe(id) as any)
          message.success('配方删除成功')
        } catch (error) {
          message.error('删除失败')
        }
      },
    })
  }

  // 处理复制配方
  const handleDuplicate = async (id: string, name: string) => {
    try {
      await dispatch(duplicateRecipe(id) as any)
      message.success(`配方"${name}"复制成功`)
    } catch (error) {
      message.error('复制失败')
    }
  }

  // 操作菜单
  const getActionMenu = (record: any) => ({
    items: [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: '查看详情',
        onClick: () => navigate(`/recipes/${record.id}`),
      },
      {
        key: 'edit',
        icon: <EditOutlined />,
        label: '编辑配方',
        onClick: () => navigate(`/recipes/${record.id}/edit`),
      },
      {
        key: 'duplicate',
        icon: <CopyOutlined />,
        label: '复制配方',
        onClick: () => handleDuplicate(record.id, record.name),
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除配方',
        danger: true,
        onClick: () => handleDelete(record.id, record.name),
      },
    ],
  })

  // 表格列配置
  const columns = [
    {
      title: '配方名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div>
          <Button 
            type="link" 
            onClick={() => navigate(`/recipes/${record.id}`)}
            style={{ padding: 0, color: '#F05654', fontWeight: 500 }}
          >
            {text}
          </Button>
          {record.description && (
            <div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {record.description}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version: number) => (
        <Tag color="blue">v{version}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '发酵温度',
      dataIndex: 'fermentationTemperature',
      key: 'fermentationTemperature',
      width: 120,
      render: (temp: number) => temp ? `${temp}°C` : '-',
    },
    {
      title: '发酵时长',
      dataIndex: 'fermentationDuration',
      key: 'fermentationDuration',
      width: 120,
      render: (duration: number) => duration ? `${duration}小时` : '-',
    },
    {
      title: '使用次数',
      dataIndex: 'batchCount',
      key: 'batchCount',
      width: 100,
      render: (count: number) => (
        <Text strong style={{ color: '#F05654' }}>{count}</Text>
      ),
    },
    {
      title: '平均评分',
      dataIndex: 'avgQualityScore',
      key: 'avgQualityScore',
      width: 120,
      render: (score: number) => score ? (
        <Text strong style={{ color: score >= 90 ? '#52c41a' : '#faad14' }}>
          {score.toFixed(1)}
        </Text>
      ) : '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right' as const,
      render: (_: any, record: any) => (
        <Dropdown menu={getActionMenu(record)} trigger={['click']}>
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ]

  // 过滤数据
  const filteredData = mockRecipes.filter(recipe => {
    const matchesSearch = recipe.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         recipe.description.toLowerCase().includes(searchText.toLowerCase())
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && recipe.isActive) ||
                         (filterStatus === 'inactive' && !recipe.isActive)
    return matchesSearch && matchesStatus
  })

  // 统计数据
  const stats = {
    total: mockRecipes.length,
    active: mockRecipes.filter(r => r.isActive).length,
    avgScore: mockRecipes.reduce((sum, r) => sum + r.avgQualityScore, 0) / mockRecipes.length,
    totalBatches: mockRecipes.reduce((sum, r) => sum + r.batchCount, 0)
  }

  if (loading) {
    return <LoadingSpinner tip="加载配方数据..." />
  }

  return (
    <div className="recipes-container">
      {/* 页面头部 */}
      <div className="recipes-header">
        <div>
          <Title level={2} className="page-title">配方管理</Title>
          <Text className="page-subtitle">管理和维护酸奶制作配方</Text>
        </div>
        <Space>
          <Button icon={<ExportOutlined />}>导出</Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/recipes/new')}
          >
            新建配方
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={12} sm={6}>
          <Card className="stat-card">
            <Statistic
              title="配方总数"
              value={stats.total}
              prefix={<ExperimentOutlined style={{ color: '#F05654' }} />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="stat-card">
            <Statistic
              title="启用配方"
              value={stats.active}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="stat-card">
            <Statistic
              title="平均评分"
              value={stats.avgScore}
              precision={1}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="stat-card">
            <Statistic
              title="总批次数"
              value={stats.totalBatches}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card className="filter-card">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索配方名称或描述"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: '100%' }}
              placeholder="筛选状态"
            >
              <Option value="all">全部状态</Option>
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
            </Select>
          </Col>
          <Col xs={24} md={10}>
            <div className="filter-info">
              <Text type="secondary">
                共找到 {filteredData.length} 个配方
              </Text>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 配方表格 */}
      <Card className="table-card">
        <Table
          columns={columns}
          dataSource={filteredData}
          rowKey="id"
          pagination={{
            total: filteredData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
        />
      </Card>
    </div>
  )
}

export default Recipes

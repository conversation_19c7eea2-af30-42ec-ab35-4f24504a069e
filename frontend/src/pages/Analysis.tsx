import React from 'react'
import { Card, Typography, Button, Space, Upload, message } from 'antd'
import { UploadOutlined, RobotOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const Analysis: React.FC = () => {
  const handleUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 文件上传成功`)
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 文件上传失败`)
    }
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>AI分析</Title>
          <Text type="secondary">上传显微镜图像进行AI质量分析</Text>
        </div>
        <Space>
          <Button 
            type="primary" 
            icon={<RobotOutlined />}
          >
            开始分析
          </Button>
        </Space>
      </div>
      
      <Card title="图像上传" style={{ marginBottom: 24 }}>
        <Upload.Dragger
          name="images"
          multiple
          accept="image/*"
          onChange={handleUpload}
          style={{ padding: 40 }}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined style={{ fontSize: 48, color: '#F05654' }} />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传显微镜图像，支持 JPG、PNG、TIFF 格式
          </p>
        </Upload.Dragger>
      </Card>

      <Card title="分析历史">
        <p>AI分析页面正在开发中...</p>
        <p>功能包括：</p>
        <ul>
          <li>多图像上传分析</li>
          <li>菌群形态识别</li>
          <li>污染检测预警</li>
          <li>置信度评估</li>
          <li>分析结果展示</li>
        </ul>
      </Card>
    </div>
  )
}

export default Analysis

import React from 'react'
import { <PERSON>, <PERSON>po<PERSON>, Button, Space } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useNavigate, useParams } from 'react-router-dom'

const { Title } = Typography

const BatchDetail: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/batches')}
          >
            返回批次列表
          </Button>
        </Space>
      </div>
      
      <Card>
        <Title level={2}>批次详情</Title>
        <p>批次ID: {id}</p>
        <p>此页面正在开发中...</p>
      </Card>
    </div>
  )
}

export default BatchDetail

import React from 'react'
import { <PERSON>, Typography, Button, Space } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Text } = Typography

const Batches: React.FC = () => {
  const navigate = useNavigate()

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>批次管理</Title>
          <Text type="secondary">管理和跟踪酸奶生产批次</Text>
        </div>
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/batches/new')}
          >
            新建批次
          </Button>
        </Space>
      </div>
      
      <Card>
        <p>批次管理页面正在开发中...</p>
        <p>功能包括：</p>
        <ul>
          <li>批次列表展示</li>
          <li>批次状态管理</li>
          <li>感官评估记录</li>
          <li>生产参数跟踪</li>
        </ul>
      </Card>
    </div>
  )
}

export default Batches

import React, { useEffect, useState } from 'react'
import { Row, Col, Card, Statistic, Progress, Table, Tag, Button, Space, Typography, Divider } from 'antd'
import { 
  ExperimentOutlined, 
  DatabaseOutlined, 
  RobotOutlined, 
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EyeOutlined,
  PlusOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import ReactECharts from 'echarts-for-react'
import { useSelector } from 'react-redux'
import { RootState } from '../store'
import LoadingSpinner from '../components/LoadingSpinner'
import './Dashboard.css'

const { Title, Text } = Typography

// 模拟数据
const mockStats = {
  totalRecipes: 24,
  activeBatches: 8,
  completedAnalyses: 156,
  qualityScore: 92.5,
  trends: {
    recipes: 12,
    batches: -5,
    analyses: 23,
    quality: 3.2
  }
}

const mockRecentBatches = [
  {
    id: '1',
    batchNumber: 'YG-2024-001',
    recipeName: '经典原味酸奶',
    status: 'fermenting',
    qualityScore: 94,
    createdAt: '2024-01-15 09:30',
  },
  {
    id: '2',
    batchNumber: 'YG-2024-002',
    recipeName: '蓝莓风味酸奶',
    status: 'completed',
    qualityScore: 91,
    createdAt: '2024-01-14 14:20',
  },
  {
    id: '3',
    batchNumber: 'YG-2024-003',
    recipeName: '希腊式酸奶',
    status: 'in_progress',
    qualityScore: null,
    createdAt: '2024-01-14 11:15',
  },
]

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()
  const { user } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // 质量趋势图表配置
  const qualityTrendOption = {
    title: {
      text: '质量趋势',
      textStyle: {
        fontSize: 16,
        fontWeight: 600,
        color: '#262626'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月1日', '1月2日', '1月3日', '1月4日', '1月5日', '1月6日', '1月7日'],
      axisLine: {
        lineStyle: {
          color: '#A8ABB0'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 80,
      max: 100,
      axisLine: {
        lineStyle: {
          color: '#A8ABB0'
        }
      }
    },
    series: [
      {
        name: '质量评分',
        type: 'line',
        data: [89, 92, 88, 94, 91, 93, 95],
        smooth: true,
        lineStyle: {
          color: '#F05654',
          width: 3
        },
        itemStyle: {
          color: '#F05654'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(240, 86, 84, 0.3)' },
              { offset: 1, color: 'rgba(240, 86, 84, 0.05)' }
            ]
          }
        }
      }
    ]
  }

  // 批次状态分布图表配置
  const batchStatusOption = {
    title: {
      text: '批次状态分布',
      textStyle: {
        fontSize: 16,
        fontWeight: 600,
        color: '#262626'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '批次状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        data: [
          { value: 12, name: '已完成', itemStyle: { color: '#52c41a' } },
          { value: 8, name: '进行中', itemStyle: { color: '#F05654' } },
          { value: 3, name: '发酵中', itemStyle: { color: '#faad14' } },
          { value: 1, name: '失败', itemStyle: { color: '#ff4d4f' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  // 批次表格列配置
  const batchColumns = [
    {
      title: '批次编号',
      dataIndex: 'batchNumber',
      key: 'batchNumber',
      render: (text: string, record: any) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/batches/${record.id}`)}
          style={{ padding: 0, color: '#F05654' }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '配方名称',
      dataIndex: 'recipeName',
      key: 'recipeName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          'completed': { color: 'success', text: '已完成' },
          'in_progress': { color: 'processing', text: '进行中' },
          'fermenting': { color: 'warning', text: '发酵中' },
          'failed': { color: 'error', text: '失败' },
        }
        const config = statusMap[status as keyof typeof statusMap]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '质量评分',
      dataIndex: 'qualityScore',
      key: 'qualityScore',
      render: (score: number | null) => 
        score ? <Text strong style={{ color: score >= 90 ? '#52c41a' : '#faad14' }}>{score}</Text> : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          type="text" 
          icon={<EyeOutlined />} 
          onClick={() => navigate(`/batches/${record.id}`)}
        >
          查看
        </Button>
      ),
    },
  ]

  if (loading) {
    return <LoadingSpinner tip="加载仪表板数据..." />
  }

  return (
    <div className="dashboard-container">
      {/* 欢迎区域 */}
      <div className="dashboard-header">
        <div>
          <Title level={2} className="dashboard-title">
            欢迎回来，{user?.name || '用户'}！
          </Title>
          <Text className="dashboard-subtitle">
            今天是 {new Date().toLocaleDateString('zh-CN', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              weekday: 'long'
            })}
          </Text>
        </div>
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/recipes/new')}
          >
            新建配方
          </Button>
          <Button 
            icon={<PlusOutlined />}
            onClick={() => navigate('/batches/new')}
          >
            新建批次
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} className="stats-row">
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="配方总数"
              value={mockStats.totalRecipes}
              prefix={<ExperimentOutlined style={{ color: '#F05654' }} />}
              suffix={
                <span className="trend-indicator positive">
                  <ArrowUpOutlined />
                  {mockStats.trends.recipes}%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="活跃批次"
              value={mockStats.activeBatches}
              prefix={<DatabaseOutlined style={{ color: '#faad14' }} />}
              suffix={
                <span className="trend-indicator negative">
                  <ArrowDownOutlined />
                  {Math.abs(mockStats.trends.batches)}%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="AI分析次数"
              value={mockStats.completedAnalyses}
              prefix={<RobotOutlined style={{ color: '#52c41a' }} />}
              suffix={
                <span className="trend-indicator positive">
                  <ArrowUpOutlined />
                  {mockStats.trends.analyses}%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="平均质量评分"
              value={mockStats.qualityScore}
              precision={1}
              prefix={<TrophyOutlined style={{ color: '#F05654' }} />}
              suffix={
                <span className="trend-indicator positive">
                  <ArrowUpOutlined />
                  {mockStats.trends.quality}%
                </span>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[24, 24]} className="charts-row">
        <Col xs={24} lg={16}>
          <Card title="质量趋势分析" className="chart-card">
            <ReactECharts option={qualityTrendOption} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="批次状态分布" className="chart-card">
            <ReactECharts option={batchStatusOption} style={{ height: '300px' }} />
          </Card>
        </Col>
      </Row>

      {/* 最近批次 */}
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card 
            title="最近批次" 
            className="table-card"
            extra={
              <Button type="link" onClick={() => navigate('/batches')}>
                查看全部
              </Button>
            }
          >
            <Table
              columns={batchColumns}
              dataSource={mockRecentBatches}
              pagination={false}
              rowKey="id"
              size="middle"
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard

/* =============================================================================
   Recipes 页面样式
   ============================================================================= */

.recipes-container {
  padding: 0;
  background: #fafafa;
  min-height: 100%;
}

/* 页面头部 */
.recipes-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.recipes-header .page-title {
  color: #262626 !important;
  margin: 0 !important;
  font-weight: 600;
  font-size: 28px;
}

.recipes-header .page-subtitle {
  color: #A8ABB0;
  font-size: 16px;
  margin-top: 8px;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  text-align: center;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-card .ant-card-body {
  padding: 20px;
}

.stat-card .ant-statistic-title {
  color: #A8ABB0;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-card .ant-statistic-content {
  color: #262626;
  font-size: 24px;
  font-weight: 700;
}

.stat-card .ant-statistic-content-prefix {
  font-size: 20px;
  margin-right: 8px;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.filter-card .ant-card-body {
  padding: 20px;
}

.filter-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 32px;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.table-card .ant-card-body {
  padding: 0;
}

/* 表格样式 */
.table-card .ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.table-card .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
  padding: 16px;
  font-size: 14px;
}

.table-card .ant-table-tbody > tr > td {
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
  vertical-align: top;
}

.table-card .ant-table-tbody > tr:hover > td {
  background: rgba(240, 86, 84, 0.05);
}

.table-card .ant-table-tbody > tr:last-child > td {
  border-bottom: none;
}

/* 表格内容样式 */
.table-card .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
  font-weight: 500;
}

.table-card .ant-btn-link:hover {
  text-decoration: underline;
}

.table-card .ant-tag {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  padding: 2px 8px;
  border: none;
}

/* 按钮样式 */
.recipes-header .ant-btn-primary {
  background: #F05654;
  border-color: #F05654;
  border-radius: 8px;
  font-weight: 500;
  height: 40px;
  padding: 0 20px;
}

.recipes-header .ant-btn-primary:hover {
  background: #FF6B69;
  border-color: #FF6B69;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 86, 84, 0.3);
}

.recipes-header .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  height: 40px;
  padding: 0 20px;
  border-color: #d9d9d9;
}

.recipes-header .ant-btn:hover {
  border-color: #F05654;
  color: #F05654;
}

/* 搜索框样式 */
.filter-card .ant-input-search {
  border-radius: 8px;
}

.filter-card .ant-input-search .ant-input {
  border-radius: 8px 0 0 8px;
}

.filter-card .ant-input-search .ant-input-search-button {
  border-radius: 0 8px 8px 0;
  background: #F05654;
  border-color: #F05654;
}

.filter-card .ant-input-search .ant-input-search-button:hover {
  background: #FF6B69;
  border-color: #FF6B69;
}

/* 选择器样式 */
.filter-card .ant-select {
  border-radius: 8px;
}

.filter-card .ant-select .ant-select-selector {
  border-radius: 8px;
}

.filter-card .ant-select-focused .ant-select-selector {
  border-color: #F05654;
  box-shadow: 0 0 0 2px rgba(240, 86, 84, 0.2);
}

/* 分页样式 */
.table-card .ant-pagination {
  margin: 16px 24px;
}

.table-card .ant-pagination .ant-pagination-item-active {
  background: #F05654;
  border-color: #F05654;
}

.table-card .ant-pagination .ant-pagination-item-active a {
  color: #ffffff;
}

.table-card .ant-pagination .ant-pagination-item:hover {
  border-color: #F05654;
}

.table-card .ant-pagination .ant-pagination-item:hover a {
  color: #F05654;
}

.table-card .ant-pagination .ant-pagination-next:hover .ant-pagination-item-link,
.table-card .ant-pagination .ant-pagination-prev:hover .ant-pagination-item-link {
  color: #F05654;
  border-color: #F05654;
}

/* 下拉菜单样式 */
.ant-dropdown-menu-item:hover {
  background-color: rgba(240, 86, 84, 0.1);
  color: #F05654;
}

.ant-dropdown-menu-item-danger:hover {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .recipes-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .recipes-header > div:last-child {
    display: flex;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .recipes-container {
    padding: 0;
  }
  
  .recipes-header {
    margin-bottom: 16px;
    padding: 20px;
    border-radius: 8px;
  }
  
  .recipes-header .page-title {
    font-size: 24px !important;
  }
  
  .recipes-header .page-subtitle {
    font-size: 14px;
  }
  
  .stats-row {
    margin-bottom: 16px;
  }
  
  .filter-card {
    margin-bottom: 16px;
    border-radius: 8px;
  }
  
  .filter-card .ant-card-body {
    padding: 16px;
  }
  
  .table-card {
    border-radius: 8px;
  }
  
  .table-card .ant-table-thead > tr > th,
  .table-card .ant-table-tbody > tr > td {
    padding: 12px 8px;
    font-size: 13px;
  }
  
  .stat-card .ant-card-body {
    padding: 16px;
  }
  
  .stat-card .ant-statistic-content {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .recipes-header {
    padding: 16px;
  }
  
  .recipes-header > div:last-child {
    flex-direction: column;
  }
  
  .recipes-header .page-title {
    font-size: 20px !important;
  }
  
  .filter-info {
    justify-content: flex-start;
    margin-top: 12px;
  }
  
  .table-card .ant-table-thead > tr > th,
  .table-card .ant-table-tbody > tr > td {
    padding: 8px 6px;
    font-size: 12px;
  }
  
  .table-card .ant-pagination {
    margin: 12px 16px;
  }
}

/* 动画效果 */
.recipes-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-card,
.table-card {
  animation: fadeIn 0.8s ease-out 0.3s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 表格行悬停效果 */
.table-card .ant-table-tbody > tr {
  transition: all 0.3s ease;
}

/* 按钮悬停效果 */
.recipes-header .ant-btn {
  transition: all 0.3s ease;
}

/* 卡片悬停效果 */
.stat-card,
.filter-card,
.table-card {
  transition: all 0.3s ease;
}

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  InputNumber, 
  Button, 
  Space, 
  Typography, 
  Row,
  Col,
  Select,
  DatePicker,
  message,
  Descriptions,
  Divider
} from 'antd'
import { 
  ArrowLeftOutlined, 
  SaveOutlined,
  ExperimentOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import dayjs from 'dayjs'
import { RootState } from '../store'
import { createBatch } from '../store/slices/batchSlice'
import { fetchRecipes } from '../store/slices/recipeSlice'
import type { CreateBatchForm, Recipe } from '../types'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const CreateBatch: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null)
  const navigate = useNavigate()
  const dispatch = useDispatch()
  
  const { recipes } = useSelector((state: RootState) => state.recipe)

  useEffect(() => {
    // 加载配方列表
    dispatch(fetchRecipes({}) as any)
  }, [dispatch])

  // 生成批次编号
  const generateBatchNumber = () => {
    const now = dayjs()
    const dateStr = now.format('YYYYMMDD')
    const timeStr = now.format('HHmm')
    return `YG-${dateStr}-${timeStr}`
  }

  // 选择配方
  const handleRecipeChange = (recipeId: string) => {
    const recipe = recipes.find(r => r.id === recipeId)
    setSelectedRecipe(recipe || null)
    
    if (recipe) {
      // 自动填充批次编号
      form.setFieldsValue({
        batchNumber: generateBatchNumber()
      })
    }
  }

  // 提交表单
  const handleSubmit = async (values: any) => {
    if (!selectedRecipe) {
      message.error('请选择配方')
      return
    }

    setLoading(true)
    try {
      const batchData: CreateBatchForm = {
        ...values,
        productionDate: values.productionDate.format('YYYY-MM-DD')
      }
      
      await dispatch(createBatch(batchData) as any)
      message.success('批次创建成功')
      navigate('/batches')
    } catch (error) {
      message.error('创建失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/batches')}
          >
            返回批次列表
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          productionDate: dayjs(),
          quantity: 1000
        }}
      >
        <Row gutter={24}>
          <Col xs={24} lg={16}>
            {/* 基本信息 */}
            <Card title="批次信息" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="recipeId"
                    label="选择配方"
                    rules={[{ required: true, message: '请选择配方' }]}
                  >
                    <Select
                      placeholder="请选择配方"
                      onChange={handleRecipeChange}
                      showSearch
                      filterOption={(input, option) =>
                        (option?.children as unknown as string)
                          ?.toLowerCase()
                          ?.includes(input.toLowerCase())
                      }
                    >
                      {recipes
                        .filter(recipe => recipe.isActive)
                        .map(recipe => (
                          <Option key={recipe.id} value={recipe.id}>
                            {recipe.name}
                          </Option>
                        ))
                      }
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="batchNumber"
                    label="批次编号"
                    rules={[{ required: true, message: '请输入批次编号' }]}
                  >
                    <Input placeholder="系统自动生成" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="productionDate"
                    label="生产日期"
                    rules={[{ required: true, message: '请选择生产日期' }]}
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="quantity"
                    label="生产数量 (ml)"
                    rules={[{ required: true, message: '请输入生产数量' }]}
                  >
                    <InputNumber
                      min={100}
                      max={10000}
                      style={{ width: '100%' }}
                      placeholder="请输入生产数量"
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="notes"
                label="备注信息"
              >
                <TextArea 
                  rows={4} 
                  placeholder="请输入生产备注或特殊说明" 
                />
              </Form.Item>
            </Card>

            {/* 配方详情 */}
            {selectedRecipe && (
              <Card title="配方详情" style={{ marginBottom: 24 }}>
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="配方名称">
                    {selectedRecipe.name}
                  </Descriptions.Item>
                  <Descriptions.Item label="版本">
                    v{selectedRecipe.version}
                  </Descriptions.Item>
                  <Descriptions.Item label="发酵温度">
                    {selectedRecipe.fermentationTemperature}°C
                  </Descriptions.Item>
                  <Descriptions.Item label="发酵时长">
                    {selectedRecipe.fermentationDuration}小时
                  </Descriptions.Item>
                </Descriptions>
                
                {selectedRecipe.description && (
                  <>
                    <Divider />
                    <Text type="secondary">{selectedRecipe.description}</Text>
                  </>
                )}
                
                <Divider />
                <Title level={5}>配料清单</Title>
                {selectedRecipe.ingredients.map((ingredient, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    <Text>
                      {ingredient.name}: {ingredient.amount} {ingredient.unit}
                    </Text>
                  </div>
                ))}
              </Card>
            )}
          </Col>

          <Col xs={24} lg={8}>
            {/* 生产指导 */}
            <Card title="生产指导" style={{ marginBottom: 24 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <ExperimentOutlined style={{ color: '#F05654', marginRight: 8 }} />
                  <Text strong>生产流程</Text>
                </div>
                <Text type="secondary">
                  1. 准备所有配料和设备<br/>
                  2. 按配方比例混合配料<br/>
                  3. 设置发酵温度和时间<br/>
                  4. 开始发酵过程<br/>
                  5. 定期检查发酵状态<br/>
                  6. 完成后进行质量检测
                </Text>
              </Space>
            </Card>

            {/* 注意事项 */}
            <Card title="注意事项" style={{ marginBottom: 24 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text type="warning">
                  • 确保所有设备已清洁消毒
                </Text>
                <Text type="warning">
                  • 严格控制发酵温度
                </Text>
                <Text type="warning">
                  • 记录实际生产参数
                </Text>
                <Text type="warning">
                  • 及时进行质量检测
                </Text>
              </Space>
            </Card>

            {/* 操作按钮 */}
            <Card>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                  block
                  size="large"
                >
                  创建批次
                </Button>
                <Button
                  block
                  onClick={() => form.resetFields()}
                >
                  重置表单
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  )
}

export default CreateBatch

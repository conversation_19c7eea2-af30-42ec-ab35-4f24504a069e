import React, { useState } from 'react'
import { Form, Input, <PERSON><PERSON>, Card, Typography, Divider, Alert, Space } from 'antd'
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '../store'
import { login, register } from '../store/slices/authSlice'
import LoadingSpinner from '../components/LoadingSpinner'
import './Login.css'

const { Title, Text, Link } = Typography

interface LoginForm {
  email: string
  password: string
}

interface RegisterForm {
  name: string
  email: string
  password: string
  confirmPassword: string
}

const Login: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true)
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { isLoading } = useSelector((state: RootState) => state.auth)

  const from = (location.state as any)?.from?.pathname || '/'

  const handleSubmit = async (values: LoginForm | RegisterForm) => {
    try {
      if (isLogin) {
        const result = await dispatch(login(values as LoginForm) as any)
        if (result.type === 'auth/login/fulfilled') {
          navigate(from, { replace: true })
        }
      } else {
        const result = await dispatch(register(values as RegisterForm) as any)
        if (result.type === 'auth/register/fulfilled') {
          navigate(from, { replace: true })
        }
      }
    } catch (error) {
      console.error('Authentication error:', error)
    }
  }

  const toggleMode = () => {
    setIsLogin(!isLogin)
    form.resetFields()
  }

  if (isLoading) {
    return <LoadingSpinner tip="正在验证用户身份..." />
  }

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          {/* Logo和标题 */}
          <div className="login-header">
            <div className="login-logo">
              <span className="logo-icon">🥛</span>
              <div className="logo-text">
                <Title level={2} className="logo-title">Yogurt AI QC</Title>
                <Text className="logo-subtitle">酸奶AI质控系统</Text>
              </div>
            </div>
            <Text className="login-description">
              通过AI驱动的显微镜图像分析，实现智能质量控制
            </Text>
          </div>

          <Divider />

          {/* 登录/注册表单 */}
          <div className="login-form-container">
            <div className="form-tabs">
              <Button 
                type={isLogin ? 'primary' : 'text'} 
                onClick={() => setIsLogin(true)}
                className="tab-button"
              >
                登录
              </Button>
              <Button 
                type={!isLogin ? 'primary' : 'text'} 
                onClick={() => setIsLogin(false)}
                className="tab-button"
              >
                注册
              </Button>
            </div>

            <Form
              form={form}
              name={isLogin ? 'login' : 'register'}
              onFinish={handleSubmit}
              layout="vertical"
              size="large"
              className="login-form"
            >
              {!isLogin && (
                <Form.Item
                  name="name"
                  label="姓名"
                  rules={[
                    { required: true, message: '请输入您的姓名' },
                    { min: 2, message: '姓名至少2个字符' },
                  ]}
                >
                  <Input 
                    prefix={<UserOutlined />} 
                    placeholder="请输入您的姓名" 
                  />
                </Form.Item>
              )}

              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入您的邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input 
                  prefix={<MailOutlined />} 
                  placeholder="请输入您的邮箱" 
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="密码"
                rules={[
                  { required: true, message: '请输入您的密码' },
                  { min: 6, message: '密码至少6个字符' },
                ]}
              >
                <Input.Password 
                  prefix={<LockOutlined />} 
                  placeholder="请输入您的密码" 
                />
              </Form.Item>

              {!isLogin && (
                <Form.Item
                  name="confirmPassword"
                  label="确认密码"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认您的密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve()
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'))
                      },
                    }),
                  ]}
                >
                  <Input.Password 
                    prefix={<LockOutlined />} 
                    placeholder="请再次输入密码" 
                  />
                </Form.Item>
              )}

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={isLoading}
                  block
                  className="submit-button"
                >
                  {isLogin ? '登录' : '注册'}
                </Button>
              </Form.Item>

              {isLogin && (
                <div className="login-footer">
                  <Link href="#" className="forgot-password">
                    忘记密码？
                  </Link>
                </div>
              )}
            </Form>

            {/* 演示账号提示 */}
            <Alert
              message="演示账号"
              description={
                <Space direction="vertical" size="small">
                  <Text>邮箱: <EMAIL></Text>
                  <Text>密码: demo123</Text>
                </Space>
              }
              type="info"
              showIcon
              className="demo-account"
            />
          </div>
        </Card>

        {/* 页脚 */}
        <div className="login-page-footer">
          <Text type="secondary">
            © 2024 Yogurt AI QC. 专为精品咖啡厅和酸奶制作商设计的智能质量控制系统
          </Text>
        </div>
      </div>
    </div>
  )
}

export default Login

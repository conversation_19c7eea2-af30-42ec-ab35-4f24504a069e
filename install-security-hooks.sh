#!/bin/bash

# =============================================================================
# Yoghurt AI QC - 安全钩子安装脚本
# =============================================================================
# 此脚本用于安装 Git 安全钩子和配置安全环境

set -e

# 颜色定义
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Yoghurt AI QC 安全配置安装程序${NC}"
echo "======================================"
echo ""

# 检查是否在 Git 仓库中
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ 错误：当前目录不是 Git 仓库${NC}"
    echo "请在项目根目录运行此脚本"
    exit 1
fi

echo -e "${GREEN}✅ 检测到 Git 仓库${NC}"

# 1. 配置 Git 钩子路径
echo -e "${YELLOW}📁 配置 Git 钩子路径...${NC}"
git config core.hooksPath .githooks
echo -e "${GREEN}✅ Git 钩子路径已设置为 .githooks${NC}"

# 2. 确保钩子文件有执行权限
echo -e "${YELLOW}🔑 设置钩子文件权限...${NC}"
if [ -f ".githooks/pre-commit" ]; then
    chmod +x .githooks/pre-commit
    echo -e "${GREEN}✅ pre-commit 钩子权限已设置${NC}"
else
    echo -e "${RED}❌ 警告：pre-commit 钩子文件不存在${NC}"
fi

# 3. 检查并备份现有的 .env 文件
echo -e "${YELLOW}🔒 检查环境变量文件...${NC}"
if [ -f ".env" ]; then
    echo -e "${YELLOW}⚠️  发现现有的 .env 文件${NC}"
    
    # 检查是否包含敏感信息
    SENSITIVE_FOUND=false
    
    # 检查常见的敏感模式
    if grep -qE "(password|key|secret|token)\s*=\s*[^\s]+" .env 2>/dev/null; then
        SENSITIVE_FOUND=true
    fi
    
    if [ "$SENSITIVE_FOUND" = true ]; then
        echo -e "${RED}🚨 警告：.env 文件中检测到可能的敏感信息${NC}"
        echo "建议操作："
        echo "1. 备份当前 .env 文件"
        echo "2. 使用 .env.secure 模板创建新的 .env 文件"
        echo "3. 手动填入安全的凭据"
        echo ""
        echo -e "${YELLOW}是否要自动备份并替换 .env 文件？ (y/N)${NC}"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            # 创建备份
            BACKUP_NAME=".env.backup.$(date +%Y%m%d_%H%M%S)"
            cp .env "$BACKUP_NAME"
            echo -e "${GREEN}✅ 已备份到 $BACKUP_NAME${NC}"
            
            # 使用安全模板
            if [ -f ".env.secure" ]; then
                cp .env.secure .env
                echo -e "${GREEN}✅ 已使用安全模板替换 .env 文件${NC}"
                echo -e "${YELLOW}⚠️  请编辑 .env 文件并填入真实的安全凭据${NC}"
            else
                echo -e "${RED}❌ 错误：找不到 .env.secure 模板文件${NC}"
            fi
        fi
    else
        echo -e "${GREEN}✅ .env 文件看起来是安全的${NC}"
    fi
else
    echo -e "${YELLOW}📝 未找到 .env 文件${NC}"
    if [ -f ".env.secure" ]; then
        echo -e "${YELLOW}是否要从安全模板创建 .env 文件？ (y/N)${NC}"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            cp .env.secure .env
            echo -e "${GREEN}✅ 已从模板创建 .env 文件${NC}"
            echo -e "${YELLOW}⚠️  请编辑 .env 文件并填入真实的安全凭据${NC}"
        fi
    fi
fi

# 4. 检查 .gitignore 配置
echo -e "${YELLOW}📋 检查 .gitignore 配置...${NC}"
if grep -q ".env" .gitignore 2>/dev/null; then
    echo -e "${GREEN}✅ .gitignore 已配置忽略 .env 文件${NC}"
else
    echo -e "${YELLOW}⚠️  .gitignore 中未找到 .env 规则${NC}"
    echo "建议手动添加环境变量文件到 .gitignore"
fi

# 5. 测试钩子
echo -e "${YELLOW}🧪 测试安全钩子...${NC}"
if [ -f ".githooks/pre-commit" ]; then
    echo "运行 pre-commit 钩子测试..."
    # 创建一个临时测试文件
    echo "# 测试文件" > .test-security-hook
    git add .test-security-hook
    
    # 运行钩子（但不实际提交）
    if .githooks/pre-commit; then
        echo -e "${GREEN}✅ 安全钩子测试通过${NC}"
    else
        echo -e "${YELLOW}⚠️  安全钩子测试有警告，这是正常的${NC}"
    fi
    
    # 清理测试文件
    git reset HEAD .test-security-hook
    rm -f .test-security-hook
fi

# 6. 显示安全建议
echo ""
echo -e "${BLUE}🛡️  安全建议${NC}"
echo "============"
echo "1. 定期更换密码和 API 密钥"
echo "2. 使用强密码（至少16字符，包含大小写字母、数字和特殊字符）"
echo "3. 永远不要在代码中硬编码敏感信息"
echo "4. 定期审查 Git 历史记录"
echo "5. 在生产环境中使用专业的密钥管理服务"
echo ""
echo -e "${BLUE}📚 相关文档${NC}"
echo "============"
echo "- 安全指南: SECURITY_GUIDE.md"
echo "- 环境变量模板: .env.secure"
echo "- Git 钩子: .githooks/pre-commit"
echo ""
echo -e "${GREEN}🎉 安全配置安装完成！${NC}"
echo -e "${YELLOW}请记得编辑 .env 文件并填入真实的安全凭据${NC}"
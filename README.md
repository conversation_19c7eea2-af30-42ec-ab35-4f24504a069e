# Yogurt AI QC - 酸奶AI质控系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com/)

> 🔬 AI驱动的酸奶质量控制系统，通过显微镜图像分析实现智能质控

## 📋 项目概述

Yogurt AI QC 是一个专为精品咖啡厅和酸奶制作商设计的智能质量控制系统。通过结合传统工艺记录与AI显微分析，将酸奶制作从"经验艺术"提升为"数据科学"，确保每一批产品的极致稳定性、安全性与独特性。

### 🎯 核心价值

- **品质一致性**: 通过数据化记录和AI反馈，消除因人员、环境等变量导致的品质波动
- **食品安全保障**: 利用AI辅助识别潜在的微生物污染，建立第一道安全防线
- **研发创新加速**: 数据化追踪配方与成品口感、菌群的关联，为开发新风味提供科学依据
- **品牌故事塑造**: "我们的每一杯酸奶，都经过AI显微镜的品质把关"

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   AI分析服务    │
│   React + TS    │◄──►│   Node.js       │◄──►│   Python        │
│   Ant Design    │    │   Express       │    │   FastAPI       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   PostgreSQL    │    │   AI模型        │
│   数据可视化    │    │   Redis缓存     │    │   OpenAI GPT-4V │
│   报告生成      │    │   文件存储      │    │   图像处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 前置要求

- **Node.js** 18+ 
- **Python** 3.11+
- **Docker** & **Docker Compose**
- **Conda** (推荐用于Python环境管理)

### 1. 克隆项目

```bash
git clone <repository-url>
cd yoghurt
```

### 2. 一键环境设置

```bash
# 运行自动化环境设置脚本
bash scripts/setup-environment.sh
```

这个脚本会自动：
- 创建Conda环境
- 安装所有依赖
- 设置Docker服务
- 初始化数据库
- 创建配置文件

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件，填入你的API密钥和安全凭据
nano .env
```

**重要配置项**：
```env
# OpenAI API密钥（必需）
OPENAI_API_KEY=sk-your-openai-api-key-here

# 数据库配置（密码通过单独的环境变量提供）
DATABASE_URL=postgresql://yoghurt_user@localhost:6432/yoghurt_qc
DB_PASSWORD=your-secure-database-password

# JWT密钥（至少32个字符）
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
```

**🔒 安全提示**：
- 使用强密码（至少12个字符）
- 定期轮换API密钥和数据库密码
- 不要将真实凭据提交到版本控制
- 生产环境使用不同的密钥

### 4. 启动服务

```bash
# 启动所有服务
npm run dev

# 或者分别启动
npm run docker:up        # 启动数据库和Redis
npm run dev:backend      # 启动后端API
npm run dev:frontend     # 启动前端应用
npm run dev:ai           # 启动AI服务
```

### 5. Docker Compose 安全配置

为了确保容器化部署的安全性，我们已经移除了所有硬编码的密码。使用Docker Compose时：

```bash
# 复制Docker环境变量模板
cp .env.docker .env.docker.local

# 编辑本地Docker环境文件
nano .env.docker.local

# 加载环境变量并启动服务
export $(cat .env.docker.local | xargs)
docker-compose up
```

**📚 详细文档**: 查看 [Docker安全指南](./docs/DOCKER_SECURITY_GUIDE.md) 了解完整的安全配置说明。

### 6. 访问应用

- **前端应用**: http://localhost:6173
- **后端API**: http://localhost:6000
- **API文档**: http://localhost:6000/api/docs
- **AI服务**: http://localhost:6800
- **AI服务文档**: http://localhost:6800/docs

## 📁 项目结构

```
yoghurt/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── store/          # Redux状态管理
│   │   └── types/          # TypeScript类型定义
│   └── package.json
├── backend/                  # Node.js后端API
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   └── middleware/     # 中间件
│   └── package.json
├── ai-service/              # Python AI分析服务
│   ├── src/
│   │   ├── services/       # AI分析服务
│   │   ├── models/         # 数据模型
│   │   └── utils/          # 工具函数
│   └── requirements.txt
├── infrastructure/          # 基础设施配置
│   ├── docker/             # Docker配置
│   ├── k8s/               # Kubernetes配置
│   └── terraform/         # Terraform配置
├── docs/                    # 项目文档
├── scripts/                 # 脚本文件
├── docker-compose.yml       # Docker Compose配置
├── environment.yml          # Conda环境配置
└── README.md
```

## 🔧 开发指南

### 开发环境

```bash
# 激活Conda环境
conda activate yoghurt-ai-qc

# 安装前端依赖
cd frontend && npm install

# 安装后端依赖
cd backend && npm install

# 安装AI服务依赖
cd ai-service && pip install -r requirements.txt
```

### 数据库管理

```bash
# 运行数据库迁移
npm run migrate

# 重置数据库
npm run db:reset

# 查看数据库
npm run db:studio
```

### 测试

```bash
# 运行所有测试
npm run test

# 运行前端测试
npm run test:frontend

# 运行后端测试
npm run test:backend

# 运行AI服务测试
cd ai-service && pytest
```

### 代码质量

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type-check
```

## 📊 功能特性

### 🧪 配方管理
- 创建和编辑酸奶配方
- 配方版本控制
- 配方搜索和筛选
- 配方使用统计

### 📋 批次管理
- 批次创建和跟踪
- 感官评估记录
- 生产参数记录
- 批次状态管理

### 🔬 AI显微分析
- 多图像上传分析
- 菌群形态识别
- 污染检测预警
- 置信度评估
- 多模型集成

### 📈 数据分析
- 质量趋势分析
- 批次对比分析
- 配方性能统计
- 自定义报告生成

### 👥 用户管理
- 多用户协作
- 角色权限控制
- 操作日志审计
- 团队管理

## 🔐 安全特性

- JWT身份认证
- API速率限制
- 数据加密存储
- CORS安全配置
- 输入验证和清理
- SQL注入防护

## 📱 API文档

完整的API文档可在以下地址查看：
- **Swagger UI**: http://localhost:6000/api/docs
- **ReDoc**: http://localhost:6000/api/redoc

### 主要API端点

```
POST /api/auth/login          # 用户登录
GET  /api/recipes             # 获取配方列表
POST /api/recipes             # 创建新配方
GET  /api/batches             # 获取批次列表
POST /api/batches             # 创建新批次
POST /api/analysis/analyze    # 提交AI分析
GET  /api/reports/quality     # 获取质量报告
```

## 🚢 部署指南

### Docker部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 生产环境部署

详细的生产环境部署指南请参考：[部署文档](docs/deployment.md)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与联系

- **文档**: [项目文档](docs/)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/yoghurt-ai-qc/issues)
- **讨论**: [GitHub Discussions](https://github.com/your-org/yoghurt-ai-qc/discussions)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**Made with ❤️ for the artisan yogurt community**
